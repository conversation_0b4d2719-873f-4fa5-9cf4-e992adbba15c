{"name": "ydcj-app", "version": "1.0.0", "scripts": {"dev": "vite --mode development --port 80 --open", "test": "vite --mode test --port 3000 --open", "build": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "build:analyze": "vite build --mode production --config vite.config.analyze.js", "preview": "vite preview", "clean": "<PERSON><PERSON><PERSON> dist", "serve": "vite preview --port 80 --open"}, "dependencies": {"axios": "^1.6.0", "qrcode": "^1.5.4", "vue": "^3.4.0", "vue-echarts": "^7.0.3", "vue-i18n": "^9.14.4", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "echarts": "^5.6.0", "pinia": "^3.0.3", "rimraf": "^5.0.10", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.89.2", "terser": "^5.29.2", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vite-svg-loader": "^5.1.0", "vue-router": "^4.5.1"}}