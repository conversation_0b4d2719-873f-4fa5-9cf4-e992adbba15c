<template>
  <div class="app-container" :class="deviceClasses">
    <transition name="fade-slide" mode="out-in">
      <router-view />
    </transition>
  </div>
</template>

<script setup>
import { computed, onMounted, provide } from 'vue'
import { useDeviceState } from './composables/useDeviceState'
import { setSafeAreaVariables } from './utils/deviceDetect'
import { useRoute } from 'vue-router'
import { navigationDirection } from './router'
import { initTheme } from './utils/theme'

// 预留全局i18n支持

// 初始化设备状态
const { deviceInfo, updateDeviceInfo } = useDeviceState()

// 计算设备相关的类名
const deviceClasses = computed(() => ({
  'is-mobile': deviceInfo.value.isMobile,
  'is-tablet': deviceInfo.value.isTablet,
  'is-desktop': deviceInfo.value.isDesktop,
  'is-landscape': deviceInfo.value.isLandscape,
  'is-portrait': deviceInfo.value.isPortrait,
  'is-ios': deviceInfo.value.isIOS,
  'is-android': deviceInfo.value.isAndroid
}))

// 提供设备信息给所有组件
provide('deviceInfo', deviceInfo)

const route = useRoute()

// 根据导航方向和路由元信息计算过渡效果名称
const transitionName = computed(() => {
  // 如果路由有自定义过渡效果，优先使用
  if (route.meta.transition) {
    return route.meta.transition
  }
  
  // 否则根据导航方向使用默认过渡效果
  return navigationDirection.value === 'forward' ? 'slide-left' : 'slide-right'
})

// 在应用挂载时更新设备信息
onMounted(() => {
  // 强制更新一次设备信息
  updateDeviceInfo()
  
  // 设置视口高度变量（解决移动端100vh问题）
  setViewportHeight()
  window.addEventListener('resize', setViewportHeight)
  
  // 设置安全区域CSS变量
  setSafeAreaVariables()
  
  // 初始化主题设置
  initTheme()
  
  console.log('App mounted, device:', deviceInfo.value)
})

// 设置视口高度CSS变量
function setViewportHeight() {
  // 使用window.innerHeight代替100vh，解决移动浏览器地址栏问题
  const vh = window.innerHeight * 0.01
  document.documentElement.style.setProperty('--vh', `${vh}px`)
}
</script>

<style>
.app-container {
  width: 100%;
  height: 100%;
  min-height: calc(var(--vh, 1vh) * 100);
  background-color: var(--app-background, #f5f5f5);
  color: var(--text-color, #333);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* 横屏模式下的特殊处理 */
.app-container.is-landscape.is-mobile {
  display: flex;
  flex-direction: row;
}

/* 为iOS设备添加平滑滚动 */
.app-container.is-ios {
  -webkit-overflow-scrolling: touch;
}

/* 路由过渡动画 */
/* 向左滑动过渡效果 */
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
  position: absolute;
  width: 100%;
}

.slide-left-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 向右滑动过渡效果 */
.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>