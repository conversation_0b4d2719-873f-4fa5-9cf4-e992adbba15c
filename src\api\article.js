import { post, get } from '../utils/http'

/**
 * 文章/栏目相关API服务
 */
const articleApi = {
  /**
   * 获取帮助中心栏目分类
   * @param {Object} data - 请求体，需包含 language 字段
   * @returns {Promise} - 返回分类列表
   */
  getHelpCategories(data) {
    return post('/article/cate', data)
  },
  /**
   * 获取指定栏目下的文章列表
   * @param {string|number} cid - 栏目ID
   * @returns {Promise} - 返回文章列表
   */
  getArticleListByCategory(cid) {
    return get('/article/lists', { params: { cid } })
  },
  /**
   * 获取文章详情
   * @param {string|number} id - 文章ID
   * @returns {Promise} - 返回文章详情
   */
  getArticleDetail(id) {
    return get('/article/detail', { params: { id } })
  }
}

export default articleApi 