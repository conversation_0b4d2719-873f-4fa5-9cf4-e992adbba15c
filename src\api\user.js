import { get, post } from '../utils/http';

/**
 * 用户相关API服务
 */
const userApi = {
  /**
   * 用户登录
   * @param {Object} data - 登录信息
   * @param {string} data.email - 用户邮箱
   * @param {string} data.password - 用户密码
   * @param {string} [data.googleCode] - 谷歌验证码（如果已绑定谷歌验证器）
   * @returns {Promise} - 返回登录结果
   */
  login(data) {
    // 构建OAuth2所需的表单数据
    const formData = new URLSearchParams();
    formData.append('username', data.email);
    formData.append('password', data.password);
    formData.append('grant_type', 'password');

    // 如果提供了Google验证码，添加到请求中
    if (data.googleCode) {
      formData.append('googleCode', data.googleCode);
    }

    // 使用表单格式发送请求
    return post('/auth/oauth/token', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'client_Id': 'customer',
        'isToken': false,
        'Authorization': 'Basic ' + window.btoa('customer:customer')
      }
    });
  },

  /**
   * 发送邮箱验证码
   * @param {Object} data - 邮箱信息
   * @param {string} data.email - 用户邮箱
   * @param {string} [data.inviteCode] - 邀请码（可选）
   * @returns {Promise} - 返回发送结果
   */
  sendEmailVerificationCode(data) {
    return post('/user/app/user/send_email_code', data);
  },

  /**
   * 验证邮箱验证码
   * @param {Object} data - 验证信息
   * @param {string} data.email - 用户邮箱
   * @param {string} data.code - 验证码
   * @returns {Promise} - 返回验证结果
   */
  verifyEmailCode(data) {
    return post('/user/app/user/check_email_code', data);
  },

  /**
   * 重置密码
   * @param {Object} data - 重置信息
   * @param {string} data.email - 用户邮箱
   * @param {string} data.code - 验证码
   * @param {string} data.newPassword - 新密码
   * @returns {Promise} - 返回重置结果
   */
  resetPassword(data) {
    return post('/user/resetPassword', data);
  },

  /**
   * 获取用户信息
   * @returns {Promise} - 返回用户信息
   */
  getUserInfo() {
    return get('/user/app/user/info');
  },

  /**
   * 更新用户信息
   * @param {Object} data - 用户信息
   * @returns {Promise} - 返回更新结果
   */
  updateUserInfo(data) {
    return post('/user/updateUser', data);
  },

  /**
   * 上传用户头像
   * @param {FormData} formData - 包含头像文件的FormData
   * @returns {Promise} - 返回上传结果
   */
  uploadImage(formData) {
    return post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 获取邀请人列表
   * @param {Object} params - 查询参数 { page, pageSize, belong }
   * @returns {Promise} - 返回邀请人列表
   */
  listByBelong({ page, pageSize, belong }) {
    return post(`/user/listByBelong?belong=${belong}`, { page, pageSize });
  },
};

export default userApi;