// 导出所有图标组件
const iconTemplate = (pathD) => `
  <svg 
    viewBox="0 0 24 24" 
    width="24" 
    height="24"
    aria-hidden="true"
    focusable="false"
  >
    <path fill="currentColor" d="${pathD}" />
  </svg>
`;

export const HomeIcon = {
  template: iconTemplate('M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z')
};

export const MarketIcon = {
  template: iconTemplate('M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z')
};

export const TradeIcon = {
  template: iconTemplate('M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10h-4v4h-2v-4H7v-2h4V7h2v4h4v2z')
};

export const AssetIcon = {
  template: iconTemplate('M21 18v1c0 1.1-.9 2-2 2H5c-1.11 0-2-.9-2-2V5c0-1.1.89-2 2-2h14c1.1 0 2 .9 2 2v1h-9c-1.11 0-2 .9-2 2v8c0 1.1.89 2 2 2h9zm-9-2h10V8H12v8zm4-2.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z')
};

export const PaymentIcon = {
  template: iconTemplate('M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z')
};

export const DollarCircleIcon = {
  template: `<svg viewBox="0 0 48 48" width="48" height="48" style="display:block;"><circle cx="24" cy="24" r="22" fill="#000"/><text x="24" y="30" text-anchor="middle" dominant-baseline="middle" font-size="28" fill="#fff" font-family="Arial Black, Arial, sans-serif" font-weight="900">$</text></svg>`
};