/**
 * 主样式文件
 * 导入全局变量和设置基础样式
 */

/* 导入变量 */
@import './variables.scss';

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: var(--font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif);
  font-size: var(--font-size-md);
  color: var(--text-color);
  background-color: var(--app-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* 使用CSS变量修复移动端100vh问题 */
.full-height {
  height: 100vh; /* 回退 */
  height: calc(var(--vh, 1vh) * 100);
}

/* 安全区域适配 */
.safe-area-padding {
  padding-top: var(--safe-area-inset-top);
  padding-right: var(--safe-area-inset-right);
  padding-bottom: var(--safe-area-inset-bottom);
  padding-left: var(--safe-area-inset-left);
}

.safe-area-padding-top {
  padding-top: var(--safe-area-inset-top);
}

.safe-area-padding-bottom {
  padding-bottom: var(--safe-area-inset-bottom);
}

/* 设备类型和方向适配类 */
.device-mobile {
  /* 移动设备特定样式 */
}

.device-tablet {
  /* 平板设备特定样式 */
}

.device-desktop {
  /* 桌面设备特定样式 */
}

.orientation-portrait {
  /* 竖屏模式特定样式 */
}

.orientation-landscape {
  /* 横屏模式特定样式 */
}

/* 常用布局类 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-grow {
  flex-grow: 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-start {
  justify-content: flex-start;
}

.flex-end {
  justify-content: flex-end;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

/* 间距类 */
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.ml-xs { margin-left: var(--spacing-xs); }
.ml-sm { margin-left: var(--spacing-sm); }
.ml-md { margin-left: var(--spacing-md); }
.ml-lg { margin-left: var(--spacing-lg); }
.ml-xl { margin-left: var(--spacing-xl); }

.mr-xs { margin-right: var(--spacing-xs); }
.mr-sm { margin-right: var(--spacing-sm); }
.mr-md { margin-right: var(--spacing-md); }
.mr-lg { margin-right: var(--spacing-lg); }
.mr-xl { margin-right: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.pt-xs { padding-top: var(--spacing-xs); }
.pt-sm { padding-top: var(--spacing-sm); }
.pt-md { padding-top: var(--spacing-md); }
.pt-lg { padding-top: var(--spacing-lg); }
.pt-xl { padding-top: var(--spacing-xl); }

.pb-xs { padding-bottom: var(--spacing-xs); }
.pb-sm { padding-bottom: var(--spacing-sm); }
.pb-md { padding-bottom: var(--spacing-md); }
.pb-lg { padding-bottom: var(--spacing-lg); }
.pb-xl { padding-bottom: var(--spacing-xl); }

.pl-xs { padding-left: var(--spacing-xs); }
.pl-sm { padding-left: var(--spacing-sm); }
.pl-md { padding-left: var(--spacing-md); }
.pl-lg { padding-left: var(--spacing-lg); }
.pl-xl { padding-left: var(--spacing-xl); }

.pr-xs { padding-right: var(--spacing-xs); }
.pr-sm { padding-right: var(--spacing-sm); }
.pr-md { padding-right: var(--spacing-md); }
.pr-lg { padding-right: var(--spacing-lg); }
.pr-xl { padding-right: var(--spacing-xl); }

/* 文本类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-md {
  font-size: var(--font-size-md);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-xxl {
  font-size: var(--font-size-xxl);
}

.text-bold {
  font-weight: bold;
}

.text-light {
  font-weight: 300;
}

.text-normal {
  font-weight: normal;
}

.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.text-info {
  color: var(--info-color);
}

.text-secondary {
  color: var(--text-color-secondary);
}

.text-light {
  color: var(--text-color-light);
}

/* 卡片样式 */
.card {
  background-color: var(--card-background);
  border-radius: var(--card-border-radius);
  box-shadow: var(--box-shadow-light);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: var(--font-size-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-duration) var(--transition-timing);
  border: none;
  outline: none;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-error {
  background-color: var(--error-color);
  color: white;
}

/* 响应式容器 */
.container {
  width: 100%;
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
}

/* 可见性控制 */
.hidden {
  display: none !important;
}

.visible-mobile {
  display: none;
}

.visible-tablet {
  display: none;
}

.visible-desktop {
  display: none;
}

.is-mobile .visible-mobile {
  display: block;
}

.is-tablet .visible-tablet {
  display: block;
}

.is-desktop .visible-desktop {
  display: block;
}

.is-mobile .hidden-mobile {
  display: none !important;
}

.is-tablet .hidden-tablet {
  display: none !important;
}

.is-desktop .hidden-desktop {
  display: none !important;
}

/* 方向特定可见性 */
.visible-portrait {
  display: none;
}

.visible-landscape {
  display: none;
}

.is-portrait .visible-portrait {
  display: block;
}

.is-landscape .visible-landscape {
  display: block;
}

.is-portrait .hidden-portrait {
  display: none !important;
}

.is-landscape .hidden-landscape {
  display: none !important;
}