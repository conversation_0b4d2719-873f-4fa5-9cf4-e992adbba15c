/* 主题变量 */
:root {
  /* 主色调 */
  --primary-color: #1989fa;
  --primary-light: #4dabff;
  --primary-dark: #0060c7;
  
  /* 功能色 */
  --success-color: #07c160;
  --warning-color: #ff976a;
  --danger-color: #ee0a24;
  --info-color: #909399;
  
  /* 中性色 */
  --text-primary: #323233;
  --text-regular: #646566;
  --text-secondary: #969799;
  --text-placeholder: #c8c9cc;
  
  /* 边框颜色 */
  --border-color: #ebedf0;
  --border-light: #f2f3f5;
  --border-dark: #dcdee0;
  
  /* 背景颜色 */
  --background-color: #f7f8fa;
  --background-color-light: #fafafa;
  --card-background: #ffffff;
  
  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.12);
  
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  
  /* 字体大小 */
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-loose: 1.8;
  
  /* 圆角 */
  --radius-sm: 2px;
  --radius-md: 4px;
  --radius-lg: 8px;
  --radius-round: 999px;
  
  /* 动画 */
  --animation-duration: 0.3s;
  --animation-timing-function: ease;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  
  /* 组件特定 */
  --navbar-height: 44px;
  --tabbar-height: 50px;
}

/* 暗黑模式 */
.dark-theme {
  /* 主色调 */
  --primary-color: #177ddc;
  --primary-light: #3c9ae8;
  --primary-dark: #0958a5;
  
  /* 功能色 */
  --success-color: #49aa19;
  --warning-color: #d87a16;
  --danger-color: #d32029;
  --info-color: #6b7280;
  
  /* 中性色 */
  --text-primary: #e5eaf3;
  --text-regular: #cfd3dc;
  --text-secondary: #a3a6ad;
  --text-placeholder: #8d9095;
  
  /* 边框颜色 */
  --border-color: #303030;
  --border-light: #3a3a3c;
  --border-dark: #232324;
  
  /* 背景颜色 */
  --background-color: #121212;
  --background-color-light: #1e1e1e;
  --card-background: #1c1c1e;
  
  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.4);
}