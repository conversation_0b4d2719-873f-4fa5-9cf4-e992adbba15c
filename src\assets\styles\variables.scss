/**
 * 全局CSS变量
 * 包含设备相关的尺寸、安全区域和主题颜色
 */

:root {
  /* 基础颜色 */
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --info-color: #1890ff;
  
  /* 文本颜色 */
  --text-color: #333333;
  --text-color-secondary: #666666;
  --text-color-light: #999999;
  
  /* 背景颜色 */
  --app-background: #f5f5f5;
  --card-background: #ffffff;
  
  /* 边框颜色 */
  --border-color: #e8e8e8;
  --border-color-light: #f0f0f0;
  
  /* 阴影 */
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  --box-shadow-light: 0 1px 4px rgba(0, 0, 0, 0.1);
  
  /* 布局尺寸 */
  --header-height: 56px;
  --tab-bar-height: 56px;
  --content-max-width: 1200px;
  --card-border-radius: 8px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  /* 过渡动画 */
  --transition-duration: 0.3s;
  --transition-timing: cubic-bezier(0.645, 0.045, 0.355, 1);
  
  /* 设备安全区域（默认值，会被deviceDetect.js动态更新） */
  --safe-area-inset-top: 0px;
  --safe-area-inset-right: 0px;
  --safe-area-inset-bottom: 0px;
  --safe-area-inset-left: 0px;
  
  /* 视口高度（解决移动浏览器100vh问题，会被App.vue动态更新） */
  --vh: 1vh;
}

/* 移动设备特定变量 */
.is-mobile {
  --header-height: 48px;
  --tab-bar-height: 50px;
  --font-size-md: 15px;
  --card-border-radius: 6px;
}

/* 平板设备特定变量 */
.is-tablet {
  --header-height: 52px;
  --tab-bar-height: 52px;
}

/* iOS设备特定变量 */
.is-ios {
  --font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

/* Android设备特定变量 */
.is-android {
  --font-family: Roboto, 'Noto Sans', sans-serif;
}

/* 横屏模式特定变量 */
.is-landscape {
  --tab-bar-height: 48px;
}

/* 暗色主题变量（预留） */
@media (prefers-color-scheme: dark) {
  :root {
    /* 暗色主题下可以覆盖这些变量 */
    /* 例如：
    --app-background: #121212;
    --card-background: #1e1e1e;
    --text-color: #ffffff;
    --text-color-secondary: #aaaaaa;
    --border-color: #333333;
    */
  }
}

/* 辅助函数 */
@function safe-area($position) {
  @if $position == top {
    @return var(--safe-area-inset-top);
  } @else if $position == right {
    @return var(--safe-area-inset-right);
  } @else if $position == bottom {
    @return var(--safe-area-inset-bottom);
  } @else if $position == left {
    @return var(--safe-area-inset-left);
  } @else {
    @return 0px;
  }
}