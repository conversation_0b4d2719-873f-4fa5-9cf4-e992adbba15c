<template>
  <div class="tab-bar" :class="{ 'landscape': deviceInfo.isLandscape }">
    <div class="tab-bar-content">
      <template v-for="tab in tabs" :key="tab.to">
        <router-link
          :to="tab.to"
          class="tab-item"
          :class="{ 
            active: $route.path === tab.to, 
            'trade-btn': tab.isTrade,
            'compact': deviceInfo.isLandscape
          }"
        >
          <div v-if="tab.isTrade" class="trade-icon" v-html="iconSvg(tab.icon)"></div>
          <span v-else class="icon" v-html="iconSvg(tab.icon)"></span>
          <span class="label">{{ tab.label }}</span>
        </router-link>
      </template>
    </div>
    <div class="safe-area-bottom"></div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { getDeviceInfo, createDeviceObserver } from '../utils/deviceDetect'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
// mock: 真实项目应从store或接口获取
const userRole = 'broker' // 'broker'（经纪人）或'trader'（普通交易者）

// 设备信息状态
const deviceInfo = ref(getDeviceInfo())

// 监听设备变化
let cleanupDeviceObserver = null
onMounted(() => {
  cleanupDeviceObserver = createDeviceObserver((info) => {
    deviceInfo.value = info
  })
})

onUnmounted(() => {
  if (cleanupDeviceObserver) {
    cleanupDeviceObserver()
  }
})

const tabs = computed(() => {
  const base = [
    { to: '/home', icon: 'home', label: t('tabbar.home') },
    { to: '/market', icon: 'market', label: t('tabbar.market') },
    { to: '/trade', icon: 'trade', label: t('tabbar.trade'), isTrade: true },
    { to: '/asset', icon: 'asset', label: t('tabbar.asset') },
  ]
  // "我的"tab
  const mine = { to: '/mine', icon: 'mine', label: t('tabbar.mine') }
  // 经纪人和普通用户都显示"我的"
  return [...base, mine]
})

function iconSvg(name) {
  // 根据设备方向调整图标大小
  const size = deviceInfo.value.isLandscape ? 20 : 24
  
  switch(name) {
    case 'home': return `<svg width="${size}" height="${size}" viewBox="0 0 24 24"><path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" stroke="currentColor" stroke-width="2" fill="none"/><path d="M9 22V12h6v10" stroke="currentColor" stroke-width="2" fill="none"/></svg>`
    case 'market': return `<svg width="${size}" height="${size}" viewBox="0 0 24 24"><path d="M2 2v17a3 3 0 003 3h17" stroke="currentColor" stroke-width="2" fill="none"/><path d="M5 17l4-4 4 4 7-7" stroke="currentColor" stroke-width="2" fill="none"/></svg>`
    case 'trade': return `<svg width="${size}" height="${size}" viewBox="0 0 24 24"><path d="M12 2v20M15 5h-4.5a3.5 3.5 0 100 7h3a3.5 3.5 0 110 7H9" stroke="#fff" stroke-width="2.5" fill="none" stroke-linecap="round"/></svg>`
    case 'asset': return `<svg width="${size}" height="${size}" viewBox="0 0 24 24"><rect x="2" y="4" width="20" height="16" rx="2" stroke="currentColor" stroke-width="2" fill="none"/><path d="M12 8v8M8 12h8" stroke="currentColor" stroke-width="2" fill="none"/></svg>`
    case 'mine': return `<svg width="${size}" height="${size}" viewBox="0 0 24 24"><circle cx="12" cy="8" r="4" stroke="currentColor" stroke-width="2" fill="none"/><path d="M4 20c0-4 4-7 8-7s8 3 8 7" stroke="currentColor" stroke-width="2" fill="none"/></svg>`
    default: return ''
  }
}
</script>

<style scoped lang="scss">
.tab-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  padding-bottom: env(safe-area-inset-bottom, 0px);
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,0.85);
}

.tab-bar-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: var(--tab-bar-height, 3.5rem);
}

.safe-area-bottom {
  height: var(--safe-area-inset-bottom, 0px);
  background: #fff;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #888;
  text-decoration: none;
  font-size: 0.75rem;
  gap: 4px;
  transition: color 0.2s;
  
  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  svg {
    stroke: #888;
    transition: stroke 0.2s;
  }
  
  &.active {
    color: #111;
    svg {
      stroke: #111;
    }
  }
  
  &.trade-btn {
    margin-top: -32px;
    
    .trade-icon {
      width: 48px;
      height: 48px;
      background: #111;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 2px;
      transition: transform 0.2s;
      
      svg {
        stroke: #fff;
      }
    }
    
    &:active .trade-icon {
      transform: scale(0.95);
    }
    
    &.compact {
      margin-top: -24px;
      
      .trade-icon {
        width: 40px;
        height: 40px;
      }
    }
    
    .label {
      margin-top: 4px;
    }
  }
}

// 媒体查询适配不同设备
@media screen and (max-width: 375px) {
  .tab-item {
    font-size: 0.7rem;
    
    &.trade-btn .trade-icon {
      width: 44px;
      height: 44px;
    }
  }
}

@media screen and (min-width: 768px) {
  .tab-item {
    font-size: 0.85rem;
    
    &.trade-btn .trade-icon {
      width: 52px;
      height: 52px;
    }
  }
}
</style>