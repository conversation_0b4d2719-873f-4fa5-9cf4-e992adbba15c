<template>
  <button
    class="app-button"
    :class="[
      `app-button--${type}`,
      `app-button--${size}`,
      {
        'app-button--block': block,
        'app-button--round': round,
        'app-button--plain': plain,
        'app-button--disabled': disabled,
        'app-button--loading': loading
      }
    ]"
    v-touch-feedback="touchFeedbackOptions"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <div class="app-button__content">
      <!-- 加载图标 -->
      <span v-if="loading" class="app-button__loading">
        <svg class="app-button__loading-icon" viewBox="0 0 24 24">
          <circle class="app-button__loading-circle" cx="12" cy="12" r="10" fill="none" stroke-width="3" />
        </svg>
      </span>
      
      <!-- 左侧图标 -->
      <span v-if="$slots.icon || icon" class="app-button__icon">
        <slot name="icon">
          <i :class="icon"></i>
        </slot>
      </span>
      
      <!-- 按钮文本 -->
      <span class="app-button__text">
        <slot></slot>
      </span>
    </div>
  </button>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  // 按钮类型
  type: {
    type: String,
    default: 'default',
    validator: (value) => {
      return ['default', 'primary', 'success', 'warning', 'danger', 'info'].includes(value);
    }
  },
  // 按钮尺寸
  size: {
    type: String,
    default: 'normal',
    validator: (value) => {
      return ['small', 'normal', 'large'].includes(value);
    }
  },
  // 是否为块级按钮（宽度100%）
  block: {
    type: Boolean,
    default: false
  },
  // 是否为圆角按钮
  round: {
    type: Boolean,
    default: false
  },
  // 是否为朴素按钮（无背景色）
  plain: {
    type: Boolean,
    default: false
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否显示加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 图标类名
  icon: {
    type: String,
    default: ''
  },
  // 触摸反馈配置
  touchFeedbackOptions: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['click']);

// 处理点击事件
const handleClick = (event) => {
  if (props.disabled || props.loading) return;
  emit('click', event);
};
</script>

<style scoped>
.app-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  font-size: 14px;
  line-height: 1.2;
  text-align: center;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  outline: none;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

.app-button__content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 尺寸 */
.app-button--small {
  height: 32px;
  font-size: 12px;
  padding: 0 12px;
}

.app-button--normal {
  height: 40px;
}

.app-button--large {
  height: 48px;
  font-size: 16px;
}

/* 块级按钮 */
.app-button--block {
  display: flex;
  width: 100%;
}

/* 圆角按钮 */
.app-button--round {
  border-radius: 999px;
}

/* 按钮类型 */
.app-button--default {
  background-color: #f5f5f5;
  color: #333;
}

.app-button--primary {
  background-color: var(--primary-color, #1989fa);
  color: #fff;
}

.app-button--success {
  background-color: #07c160;
  color: #fff;
}

.app-button--warning {
  background-color: #ff976a;
  color: #fff;
}

.app-button--danger {
  background-color: #ee0a24;
  color: #fff;
}

.app-button--info {
  background-color: #909399;
  color: #fff;
}

/* 朴素按钮 */
.app-button--plain {
  background-color: transparent;
  border: 1px solid currentColor;
}

.app-button--plain.app-button--primary {
  color: var(--primary-color, #1989fa);
}

.app-button--plain.app-button--success {
  color: #07c160;
}

.app-button--plain.app-button--warning {
  color: #ff976a;
}

.app-button--plain.app-button--danger {
  color: #ee0a24;
}

.app-button--plain.app-button--info {
  color: #909399;
}

/* 禁用状态 */
.app-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 加载状态 */
.app-button--loading {
  cursor: default;
}

.app-button__loading {
  margin-right: 8px;
}

.app-button__loading-icon {
  width: 16px;
  height: 16px;
  animation: app-button-loading 1s linear infinite;
}

.app-button__loading-circle {
  stroke: currentColor;
  stroke-dasharray: 60, 200;
  stroke-dashoffset: 0;
  stroke-linecap: round;
}

.app-button__icon {
  margin-right: 4px;
}

@keyframes app-button-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>