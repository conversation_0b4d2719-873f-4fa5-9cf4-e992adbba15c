<template>
  <div 
    class="list-item"
    :class="{
      'list-item--clickable': clickable || to,
      'list-item--disabled': disabled,
      'list-item--border': border
    }"
    v-touch-feedback="clickable || to ? touchFeedbackOptions : false"
    @click="handleClick"
  >
    <!-- 左侧图标 -->
    <div v-if="$slots.icon || icon" class="list-item__icon">
      <slot name="icon">
        <i :class="icon"></i>
      </slot>
    </div>
    
    <!-- 左侧图片 -->
    <div v-if="$slots.image || image" class="list-item__image">
      <slot name="image">
        <img :src="image" :alt="title" />
      </slot>
    </div>
    
    <!-- 内容区域 -->
    <div class="list-item__content">
      <!-- 标题和描述 -->
      <div class="list-item__text">
        <div v-if="$slots.title || title" class="list-item__title">
          <slot name="title">{{ title }}</slot>
        </div>
        <div v-if="$slots.description || description" class="list-item__description">
          <slot name="description">{{ description }}</slot>
        </div>
      </div>
      
      <!-- 右侧内容 -->
      <div v-if="$slots.right || rightText || rightIcon" class="list-item__right">
        <slot name="right">
          <span v-if="rightText" class="list-item__right-text">{{ rightText }}</span>
          <i v-if="rightIcon" :class="rightIcon" class="list-item__right-icon"></i>
          <i v-if="to && !rightIcon" class="list-item__arrow"></i>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps({
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 描述文本
  description: {
    type: String,
    default: ''
  },
  // 左侧图标类名
  icon: {
    type: String,
    default: ''
  },
  // 左侧图片URL
  image: {
    type: String,
    default: ''
  },
  // 右侧文本
  rightText: {
    type: String,
    default: ''
  },
  // 右侧图标类名
  rightIcon: {
    type: String,
    default: ''
  },
  // 是否可点击
  clickable: {
    type: Boolean,
    default: false
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否显示下边框
  border: {
    type: Boolean,
    default: true
  },
  // 路由跳转目标
  to: {
    type: [String, Object],
    default: ''
  },
  // 触摸反馈配置
  touchFeedbackOptions: {
    type: Object,
    default: () => ({
      color: 'rgba(0, 0, 0, 0.04)'
    })
  }
});

const emit = defineEmits(['click']);
const router = useRouter();

// 处理点击事件
const handleClick = (event) => {
  if (props.disabled) return;
  
  emit('click', event);
  
  // 如果设置了to属性，则进行路由跳转
  if (props.to) {
    router.push(props.to);
  }
};
</script>

<style scoped>
.list-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  box-sizing: border-box;
}

.list-item--border {
  border-bottom: 1px solid #ebedf0;
}

.list-item--clickable {
  cursor: pointer;
}

.list-item--disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.list-item__icon {
  margin-right: 12px;
  font-size: 20px;
  color: #969799;
}

.list-item__image {
  margin-right: 12px;
  width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: 4px;
}

.list-item__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.list-item__content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
}

.list-item__text {
  flex: 1;
  overflow: hidden;
}

.list-item__title {
  font-size: 14px;
  line-height: 1.4;
  color: #323233;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.list-item__description {
  margin-top: 4px;
  font-size: 12px;
  line-height: 1.4;
  color: #969799;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.list-item__right {
  flex-shrink: 0;
  margin-left: 8px;
  display: flex;
  align-items: center;
}

.list-item__right-text {
  font-size: 14px;
  color: #969799;
}

.list-item__right-icon {
  font-size: 16px;
  color: #969799;
}

.list-item__arrow {
  width: 8px;
  height: 8px;
  border-top: 1px solid #c8c9cc;
  border-right: 1px solid #c8c9cc;
  transform: rotate(45deg);
}
</style>