<template>
  <div 
    class="page-container"
    :class="{
      'page-container--has-navbar': showNavbar,
      'page-container--has-tabbar': hasTabbar,
      'page-container--safe-area': safeArea
    }"
  >
    <!-- 导航栏 -->
    <div 
      v-if="showNavbar" 
      class="page-navbar"
      :class="{ 'page-navbar--fixed': fixedNavbar }"
    >
      <!-- 返回按钮 -->
      <div 
        v-if="showBack" 
        class="page-navbar__back"
        v-touch-feedback="{ color: 'rgba(0, 0, 0, 0.04)' }"
        @click="handleBack"
      >
        <slot name="back-icon">
          <i class="page-navbar__back-icon"></i>
        </slot>
      </div>
      
      <!-- 标题 -->
      <div class="page-navbar__title">
        <slot name="title">{{ title }}</slot>
      </div>
      
      <!-- 右侧按钮 -->
      <div class="page-navbar__right">
        <slot name="right"></slot>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div 
      class="page-content"
      :class="{
        'page-content--pull-refresh': enablePullRefresh,
        'page-content--refreshing': isRefreshing
      }"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @touchcancel="handleTouchEnd"
    >
      <!-- 下拉刷新指示器 -->
      <div v-if="enablePullRefresh" class="page-pull-refresh">
        <div class="page-pull-refresh__indicator">
          <svg 
            class="page-pull-refresh__icon" 
            viewBox="0 0 24 24"
            :style="{ transform: `rotate(${refreshAngle}deg)` }"
          >
            <path 
              d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"
              fill="currentColor"
            />
          </svg>
          <span class="page-pull-refresh__text">
            {{ isRefreshing ? '刷新中...' : '下拉刷新' }}
          </span>
        </div>
      </div>
      
      <!-- 页面主体内容 -->
      <div class="page-content__inner">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps({
  // 页面标题
  title: {
    type: String,
    default: ''
  },
  // 是否显示导航栏
  showNavbar: {
    type: Boolean,
    default: true
  },
  // 是否固定导航栏
  fixedNavbar: {
    type: Boolean,
    default: true
  },
  // 是否显示返回按钮
  showBack: {
    type: Boolean,
    default: true
  },
  // 是否启用下拉刷新
  enablePullRefresh: {
    type: Boolean,
    default: false
  },
  // 是否有底部标签栏
  hasTabbar: {
    type: Boolean,
    default: false
  },
  // 是否启用安全区域适配
  safeArea: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['back', 'refresh']);
const router = useRouter();

// 下拉刷新相关状态
const isRefreshing = ref(false);
const startY = ref(0);
const currentY = ref(0);
const refreshThreshold = 60; // 触发刷新的阈值
const refreshAngle = computed(() => {
  if (isRefreshing.value) {
    return 0; // 刷新中，图标不旋转
  }
  
  const distance = Math.max(0, currentY.value - startY.value);
  const percentage = Math.min(distance / refreshThreshold, 1);
  return percentage * 180; // 根据下拉距离旋转图标
});

// 处理返回按钮点击
const handleBack = () => {
  emit('back');
  router.back();
};

// 处理触摸开始
const handleTouchStart = (event) => {
  if (!props.enablePullRefresh || isRefreshing.value) return;
  
  // 只有在页面顶部才能下拉刷新
  const scrollTop = event.currentTarget.scrollTop;
  if (scrollTop > 0) return;
  
  startY.value = event.touches[0].clientY;
  currentY.value = startY.value;
};

// 处理触摸移动
const handleTouchMove = (event) => {
  if (!props.enablePullRefresh || isRefreshing.value) return;
  
  // 只有在页面顶部才能下拉刷新
  const scrollTop = event.currentTarget.scrollTop;
  if (scrollTop > 0) return;
  
  currentY.value = event.touches[0].clientY;
  
  // 阻止下拉时的页面滚动
  const distance = currentY.value - startY.value;
  if (distance > 0) {
    event.preventDefault();
  }
};

// 处理触摸结束
const handleTouchEnd = () => {
  if (!props.enablePullRefresh || isRefreshing.value) return;
  
  const distance = currentY.value - startY.value;
  
  // 如果下拉距离超过阈值，触发刷新
  if (distance >= refreshThreshold) {
    startRefresh();
  }
  
  // 重置位置
  currentY.value = startY.value;
};

// 开始刷新
const startRefresh = () => {
  isRefreshing.value = true;
  emit('refresh');
  
  // 模拟刷新完成
  setTimeout(() => {
    isRefreshing.value = false;
  }, 2000);
};
</script>

<style scoped>
.page-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f7f8fa;
}

/* 导航栏 */
.page-navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.page-navbar--fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}

.page-navbar__back {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
}

.page-navbar__back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #323233;
  border-left: 2px solid #323233;
  transform: rotate(-45deg);
}

.page-navbar__title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page-navbar__right {
  display: flex;
  align-items: center;
  padding-right: 16px;
}

/* 内容区域 */
.page-content {
  position: relative;
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.page-container--has-navbar .page-content {
  height: calc(100% - 44px);
  margin-top: 44px;
}

.page-container--has-tabbar .page-content {
  height: calc(100% - 50px);
  margin-bottom: 50px;
}

/* 下拉刷新 */
.page-pull-refresh {
  position: absolute;
  left: 0;
  width: 100%;
  height: 50px;
  transform: translateY(-100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-pull-refresh__indicator {
  display: flex;
  align-items: center;
  color: #969799;
}

.page-pull-refresh__icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  transition: transform 0.3s;
}

.page-content--refreshing .page-pull-refresh__icon {
  animation: page-refresh-rotate 1s linear infinite;
}

@keyframes page-refresh-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 安全区域适配 */
.page-container--safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}
</style>