<template>
  <div class="tab-bar" :class="{ 'tab-bar--fixed': fixed }">
    <slot></slot>
  </div>
</template>

<script setup>
defineProps({
  // 是否固定在底部
  fixed: {
    type: Boolean,
    default: true
  }
});
</script>

<style scoped>
.tab-bar {
  display: flex;
  width: 100%;
  height: 50px;
  background-color: #fff;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.tab-bar--fixed {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 100;
}
</style>