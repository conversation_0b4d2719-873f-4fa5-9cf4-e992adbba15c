<template>
  <div 
    class="tab-bar-item" 
    :class="{ 'tab-bar-item--active': isActive }"
    v-touch-feedback="touchFeedbackOptions"
    @click="handleClick"
  >
    <!-- 图标 -->
    <div class="tab-bar-item__icon">
      <slot name="icon" :active="isActive">
        <i v-if="isActive && activeIcon" :class="activeIcon"></i>
        <i v-else-if="icon" :class="icon"></i>
      </slot>
      
      <!-- 徽章 -->
      <div 
        v-if="badge" 
        class="tab-bar-item__badge"
        :class="{
          'tab-bar-item__badge--dot': badge === true,
          'tab-bar-item__badge--number': typeof badge === 'number'
        }"
      >
        <template v-if="typeof badge === 'number'">
          {{ badge > 99 ? '99+' : badge }}
        </template>
      </div>
    </div>
    
    <!-- 文本 -->
    <div class="tab-bar-item__text">
      <slot>{{ title }}</slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const props = defineProps({
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 默认图标类名
  icon: {
    type: String,
    default: ''
  },
  // 激活状态图标类名
  activeIcon: {
    type: String,
    default: ''
  },
  // 徽章内容：true表示小红点，数字表示数量
  badge: {
    type: [Boolean, Number, String],
    default: null
  },
  // 路由名称或路径
  to: {
    type: [String, Object],
    default: ''
  },
  // 触摸反馈配置
  touchFeedbackOptions: {
    type: Object,
    default: () => ({
      color: 'rgba(0, 0, 0, 0.04)'
    })
  }
});

const emit = defineEmits(['click']);
const router = useRouter();
const route = useRoute();

// 判断当前项是否激活
const isActive = computed(() => {
  if (!props.to) return false;
  
  const toPath = typeof props.to === 'string' ? props.to : props.to.path;
  
  // 如果是首页路径，需要精确匹配
  if (toPath === '/' || toPath === '') {
    return route.path === '/';
  }
  
  // 其他情况，检查当前路径是否以目标路径开头
  return route.path.startsWith(toPath);
});

// 处理点击事件
const handleClick = () => {
  emit('click');
  
  if (props.to) {
    router.push(props.to);
  }
};
</script>

<style scoped>
.tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #646566;
  font-size: 12px;
}

.tab-bar-item--active {
  color: var(--primary-color, #1989fa);
}

.tab-bar-item__icon {
  position: relative;
  margin-bottom: 4px;
  font-size: 22px;
  line-height: 1;
}

.tab-bar-item__text {
  line-height: 1;
}

.tab-bar-item__badge {
  position: absolute;
  top: -4px;
  right: -6px;
  box-sizing: border-box;
  min-width: 16px;
  padding: 0 4px;
  color: #fff;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  background-color: #ee0a24;
  border-radius: 16px;
  transform: translate(50%, -50%);
}

.tab-bar-item__badge--dot {
  width: 8px;
  height: 8px;
  min-width: auto;
  padding: 0;
  border-radius: 100%;
}
</style>