<template>
  <div class="theme-switch">
    <div class="theme-switch__options">
      <button 
        class="theme-switch__option" 
        :class="{ 'theme-switch__option--active': currentTheme === THEME_TYPE.LIGHT }"
        @click="changeTheme(THEME_TYPE.LIGHT)"
      >
        <i class="iconfont icon-sun"></i>
        <span>浅色</span>
      </button>
      
      <button 
        class="theme-switch__option" 
        :class="{ 'theme-switch__option--active': currentTheme === THEME_TYPE.SYSTEM }"
        @click="changeTheme(THEME_TYPE.SYSTEM)"
      >
        <i class="iconfont icon-system"></i>
        <span>跟随系统</span>
      </button>
      
      <button 
        class="theme-switch__option" 
        :class="{ 'theme-switch__option--active': currentTheme === THEME_TYPE.DARK }"
        @click="changeTheme(THEME_TYPE.DARK)"
      >
        <i class="iconfont icon-moon"></i>
        <span>深色</span>
      </button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { THEME_TYPE, getThemeSetting, setTheme } from '@/utils/theme';

export default {
  name: 'ThemeSwitch',
  
  setup() {
    // 当前主题
    const currentTheme = ref(THEME_TYPE.SYSTEM);
    
    // 初始化
    onMounted(() => {
      // 获取当前主题设置
      currentTheme.value = getThemeSetting();
    });
    
    // 切换主题
    const changeTheme = (theme) => {
      currentTheme.value = theme;
      setTheme(theme);
    };
    
    return {
      currentTheme,
      changeTheme,
      THEME_TYPE
    };
  }
};
</script>

<style lang="scss" scoped>
.theme-switch {
  padding: var(--spacing-md);
  
  &__options {
    display: flex;
    justify-content: space-between;
    background-color: var(--gray-100);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
  }
  
  &__option {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-xs);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    transition: all var(--animation-duration) var(--animation-timing-function);
    
    i {
      font-size: var(--font-size-xl);
      margin-bottom: var(--spacing-xxs);
    }
    
    span {
      font-size: var(--font-size-sm);
    }
    
    &--active {
      background-color: var(--background-color);
      color: var(--primary-color);
      box-shadow: var(--shadow-sm);
    }
  }
}

// 深色模式下的样式调整
:global(.dark-theme) {
  .theme-switch {
    &__options {
      background-color: var(--gray-800);
    }
    
    &__option {
      &--active {
        background-color: var(--gray-900);
      }
    }
  }
}
</style>