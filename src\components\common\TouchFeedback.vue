<template>
  <div 
    class="touch-feedback-wrapper"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchEnd"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseEnd"
    @mouseleave="handleMouseEnd"
  >
    <slot></slot>
    <div 
      v-for="(ripple, index) in ripples" 
      :key="index" 
      class="ripple-effect"
      :style="{
        left: `${ripple.x}px`,
        top: `${ripple.y}px`,
        width: `${ripple.size}px`,
        height: `${ripple.size}px`,
        opacity: ripple.opacity,
        backgroundColor: color
      }"
    ></div>
  </div>
</template>

<script setup>
import { ref, onBeforeUnmount } from 'vue';

const props = defineProps({
  // 波纹颜色，默认为半透明白色
  color: {
    type: String,
    default: 'rgba(255, 255, 255, 0.35)'
  },
  // 波纹持续时间（毫秒）
  duration: {
    type: Number,
    default: 600
  },
  // 是否禁用波纹效果
  disabled: {
    type: Boolean,
    default: false
  }
});

// 存储所有活动的波纹
const ripples = ref([]);
// 存储定时器ID，用于清理
const timers = [];

// 处理触摸开始事件
const handleTouchStart = (event) => {
  if (props.disabled) return;
  
  const touch = event.touches[0];
  createRipple(touch.clientX, touch.clientY, event.currentTarget);
};

// 处理鼠标按下事件
const handleMouseDown = (event) => {
  if (props.disabled) return;
  
  createRipple(event.clientX, event.clientY, event.currentTarget);
};

// 创建波纹效果
const createRipple = (clientX, clientY, element) => {
  const rect = element.getBoundingClientRect();
  const x = clientX - rect.left;
  const y = clientY - rect.top;
  
  // 计算波纹大小，取容器宽高的较大值的2倍，确保波纹能覆盖整个元素
  const size = Math.max(rect.width, rect.height) * 2;
  
  // 创建新波纹
  const ripple = {
    x: x - size / 2,
    y: y - size / 2,
    size: 0,
    opacity: 0.5
  };
  
  ripples.value.push(ripple);
  
  // 动画：波纹扩散
  requestAnimationFrame(() => {
    ripple.size = size;
    
    // 设置淡出定时器
    const timer = setTimeout(() => {
      ripple.opacity = 0;
      
      // 动画结束后移除波纹
      const cleanupTimer = setTimeout(() => {
        const index = ripples.value.indexOf(ripple);
        if (index !== -1) {
          ripples.value.splice(index, 1);
        }
      }, 300); // 淡出动画时间
      
      timers.push(cleanupTimer);
    }, props.duration);
    
    timers.push(timer);
  });
};

// 处理触摸结束事件
const handleTouchEnd = () => {
  // 可以在这里添加触摸结束时的额外效果
};

// 处理鼠标结束事件
const handleMouseEnd = () => {
  // 可以在这里添加鼠标结束时的额外效果
};

// 组件卸载前清理所有定时器
onBeforeUnmount(() => {
  timers.forEach(timer => clearTimeout(timer));
});
</script>

<style scoped>
.touch-feedback-wrapper {
  position: relative;
  overflow: hidden;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.ripple-effect {
  position: absolute;
  border-radius: 50%;
  transform: scale(0);
  pointer-events: none;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
  transform: scale(1);
}
</style>