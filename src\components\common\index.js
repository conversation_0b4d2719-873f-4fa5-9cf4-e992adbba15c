import AppButton from './AppButton.vue';
import ListItem from './ListItem.vue';
import TabBar from './TabBar.vue';
import TabBarItem from './TabBarItem.vue';
import PageContainer from './PageContainer.vue';
import TouchFeedback from './TouchFeedback.vue';
import ThemeSwitch from './ThemeSwitch.vue';

// 组件列表
const components = [
  AppButton,
  ListItem,
  TabBar,
  TabBarItem,
  PageContainer,
  TouchFeedback,
  ThemeSwitch
];

// 安装函数
const install = (app) => {
  components.forEach(component => {
    app.component(component.name || component.__name, component);
  });
};

// 默认导出安装函数
export default {
  install
};

// 单独导出组件
export {
  AppButton,
  ListItem,
  TabBar,
  TabBarItem,
  PageContainer,
  TouchFeedback
};