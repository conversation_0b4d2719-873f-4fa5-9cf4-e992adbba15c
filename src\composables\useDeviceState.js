/**
 * 设备状态管理组合式API
 * 提供响应式的设备信息和屏幕方向变化监听
 */
import { ref, onMounted, onUnmounted, readonly } from 'vue'
import { getDeviceInfo, createDeviceObserver, setSafeAreaVariables } from '../utils/deviceDetect'

// 创建一个全局单例状态，确保在整个应用中共享相同的设备状态
const deviceInfo = ref(getDeviceInfo())

// 设备观察器清理函数
let cleanupDeviceObserver = null

/**
 * 设备状态组合式API
 * @returns {Object} 设备状态相关的方法和属性
 */
export function useDeviceState() {
  // 强制更新设备信息
  const updateDeviceInfo = () => {
    deviceInfo.value = getDeviceInfo()
    return deviceInfo.value
  }
  
  // 在组件挂载时设置设备观察器
  onMounted(() => {
    // 如果还没有设置观察器，则创建一个
    if (!cleanupDeviceObserver) {
      cleanupDeviceObserver = createDeviceObserver((newInfo) => {
        deviceInfo.value = newInfo
      })
      
      // 设置安全区域CSS变量
      setSafeAreaVariables()
    }
  })
  
  // 在最后一个使用此组合式API的组件卸载时清理观察器
  onUnmounted(() => {
    // 注意：在实际应用中，你可能需要一个更复杂的引用计数系统
    // 这里简化处理，假设只有一个组件使用此API
    if (cleanupDeviceObserver) {
      cleanupDeviceObserver()
      cleanupDeviceObserver = null
    }
  })
  
  // 返回只读的设备信息和更新方法
  return {
    deviceInfo: readonly(deviceInfo),
    updateDeviceInfo
  }
}

/**
 * 检查是否为移动设备
 * @returns {Boolean} 是否为移动设备
 */
export function isMobileDevice() {
  return deviceInfo.value.isMobile
}

/**
 * 检查是否为平板设备
 * @returns {Boolean} 是否为平板设备
 */
export function isTabletDevice() {
  return deviceInfo.value.isTablet
}

/**
 * 检查是否为桌面设备
 * @returns {Boolean} 是否为桌面设备
 */
export function isDesktopDevice() {
  return deviceInfo.value.isDesktop
}

/**
 * 检查是否为横屏模式
 * @returns {Boolean} 是否为横屏模式
 */
export function isLandscapeMode() {
  return deviceInfo.value.isLandscape
}

/**
 * 检查是否为竖屏模式
 * @returns {Boolean} 是否为竖屏模式
 */
export function isPortraitMode() {
  return deviceInfo.value.isPortrait
}

/**
 * 获取当前设备类型
 * @returns {String} 设备类型 ('mobile', 'tablet', 'desktop')
 */
export function getDeviceType() {
  if (deviceInfo.value.isMobile) return 'mobile'
  if (deviceInfo.value.isTablet) return 'tablet'
  return 'desktop'
}

/**
 * 获取当前屏幕方向
 * @returns {String} 屏幕方向 ('landscape', 'portrait')
 */
export function getScreenOrientation() {
  return deviceInfo.value.isLandscape ? 'landscape' : 'portrait'
}