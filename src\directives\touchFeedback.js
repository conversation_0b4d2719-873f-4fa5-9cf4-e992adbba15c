/**
 * 触摸反馈指令
 * 为元素添加触摸反馈效果，包括按下状态和波纹效果
 * 
 * 使用方法：
 * v-touch-feedback 或 v-touch-feedback="{ color: 'rgba(0, 0, 0, 0.1)', duration: 800 }"
 */

// 存储元素与其相关数据的映射
const elementMap = new WeakMap();

// 创建波纹元素
function createRipple(event, el, options) {
  const rect = el.getBoundingClientRect();
  const clientX = event.touches ? event.touches[0].clientX : event.clientX;
  const clientY = event.touches ? event.touches[0].clientY : event.clientY;
  
  // 计算相对于元素的点击位置
  const x = clientX - rect.left;
  const y = clientY - rect.top;
  
  // 计算波纹大小，取容器宽高的较大值的2倍，确保波纹能覆盖整个元素
  const size = Math.max(rect.width, rect.height) * 2;
  
  // 创建波纹元素
  const ripple = document.createElement('div');
  ripple.classList.add('v-touch-feedback-ripple');
  ripple.style.left = `${x - size / 2}px`;
  ripple.style.top = `${y - size / 2}px`;
  ripple.style.width = `${size}px`;
  ripple.style.height = `${size}px`;
  ripple.style.backgroundColor = options.color || 'rgba(255, 255, 255, 0.35)';
  
  // 添加波纹元素到容器
  el.appendChild(ripple);
  
  // 触发重绘，确保过渡效果生效
  ripple.offsetHeight;
  
  // 开始波纹动画
  ripple.classList.add('v-touch-feedback-ripple-active');
  
  // 设置定时器，在动画结束后移除波纹元素
  const duration = options.duration || 600;
  setTimeout(() => {
    ripple.classList.add('v-touch-feedback-ripple-fade');
    
    setTimeout(() => {
      if (el.contains(ripple)) {
        el.removeChild(ripple);
      }
    }, 300); // 淡出动画时间
    
  }, duration);
  
  return ripple;
}

// 添加按下状态
function addPressedState(el) {
  el.classList.add('v-touch-feedback-pressed');
}

// 移除按下状态
function removePressedState(el) {
  el.classList.remove('v-touch-feedback-pressed');
}

// 处理触摸/鼠标开始事件
function handleStart(event, el, options) {
  // 添加按下状态
  addPressedState(el);
  
  // 创建波纹
  if (!options.noRipple) {
    const ripple = createRipple(event, el, options);
    const data = elementMap.get(el) || {};
    data.ripple = ripple;
    elementMap.set(el, data);
  }
}

// 处理触摸/鼠标结束事件
function handleEnd(el) {
  // 移除按下状态
  removePressedState(el);
}

// 添加事件监听器
function addEventListeners(el, options) {
  const data = {
    options,
    touchStartHandler: (e) => handleStart(e, el, options),
    touchEndHandler: () => handleEnd(el),
    mouseDownHandler: (e) => {
      // 只处理左键点击
      if (e.button === 0) {
        handleStart(e, el, options);
      }
    },
    mouseUpHandler: () => handleEnd(el),
    mouseLeaveHandler: () => handleEnd(el)
  };
  
  el.addEventListener('touchstart', data.touchStartHandler);
  el.addEventListener('touchend', data.touchEndHandler);
  el.addEventListener('touchcancel', data.touchEndHandler);
  el.addEventListener('mousedown', data.mouseDownHandler);
  el.addEventListener('mouseup', data.mouseUpHandler);
  el.addEventListener('mouseleave', data.mouseLeaveHandler);
  
  elementMap.set(el, data);
}

// 移除事件监听器
function removeEventListeners(el) {
  const data = elementMap.get(el);
  if (!data) return;
  
  el.removeEventListener('touchstart', data.touchStartHandler);
  el.removeEventListener('touchend', data.touchEndHandler);
  el.removeEventListener('touchcancel', data.touchEndHandler);
  el.removeEventListener('mousedown', data.mouseDownHandler);
  el.removeEventListener('mouseup', data.mouseUpHandler);
  el.removeEventListener('mouseleave', data.mouseLeaveHandler);
  
  elementMap.delete(el);
}

// 添加必要的样式
function addStyles() {
  if (document.getElementById('v-touch-feedback-styles')) return;
  
  const styleEl = document.createElement('style');
  styleEl.id = 'v-touch-feedback-styles';
  styleEl.textContent = `
    .v-touch-feedback {
      position: relative;
      overflow: hidden;
      user-select: none;
      -webkit-tap-highlight-color: transparent;
      transition: background-color 0.3s;
    }
    
    .v-touch-feedback-pressed {
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    .v-touch-feedback-ripple {
      position: absolute;
      border-radius: 50%;
      transform: scale(0);
      pointer-events: none;
      opacity: 0.5;
      transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .v-touch-feedback-ripple-active {
      transform: scale(1);
    }
    
    .v-touch-feedback-ripple-fade {
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  `;
  
  document.head.appendChild(styleEl);
}

// 指令定义
export const touchFeedback = {
  mounted(el, binding) {
    // 添加全局样式
    addStyles();
    
    // 添加基础类
    el.classList.add('v-touch-feedback');
    
    // 解析选项
    const options = binding.value || {};
    
    // 添加事件监听器
    addEventListeners(el, options);
  },
  
  updated(el, binding) {
    // 更新选项
    const data = elementMap.get(el);
    if (data) {
      data.options = binding.value || {};
      elementMap.set(el, data);
    }
  },
  
  unmounted(el) {
    // 移除事件监听器
    removeEventListeners(el);
    
    // 移除基础类
    el.classList.remove('v-touch-feedback');
  }
};

export default touchFeedback;