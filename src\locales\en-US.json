{"reward": {"title": "My Rewards", "subtitle": "Earn more rewards and enjoy more benefits", "totalIncentive": "Total Incentive", "totalIncentiveDesc": "Total incentive earned through inviting friends", "level1Incentive": "Level 1 Incentive", "level1IncentiveDesc": "Incentive earned through your direct friend invitations", "level2Incentive": "Level 2 Incentive", "level2IncentiveDesc": "Incentive earned through level 1 friends inviting friends", "level3Incentive": "Level 3 Incentive", "level3IncentiveDesc": "Incentive earned through level 2 friends inviting friends", "myBenefits": "My Benefits", "brokerLevel": "My Broker Level", "level1Cashback": "Level 1 Cashback Rate"}, "login": {"welcome": "Welcome back", "subtitle": "Sign in to your account", "email": "Email Address", "emailPlaceholder": "Enter your email", "password": "Password", "passwordPlaceholder": "Enter your password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "submit": "Sign in", "or": "OR", "noAccount": "Don't have an account?", "register": "Register now", "emailRequired": "Please enter your email address", "emailInvalid": "Please enter a valid email address", "passwordRequired": "Please enter your password", "passwordLength": "Password must be at least 8 characters", "google": "Sign in with Google", "apple": "Sign in with Apple"}, "home": {"menu": "<PERSON><PERSON>", "product": "Product", "title": "Welcome", "subtitle": "Start your digital asset journey", "description": "Our platform", "quickActions": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "transfer": "Transfer", "trade": "Trade"}, "recentActivity": "Recent Activity", "viewAll": "View All", "noActivity": "No activity"}, "common": {"login": "Sign in", "register": "Register", "back": "Back", "loading": "Loading...", "loadError": "Failed to load, please try again.", "alerts": {"withdrawSuccess": "Withdrawal request submitted!", "transferSuccess": "Transfer successful!", "paymentSuccess": "Deposit successful!"}, "console": {"withdrawConfirm": "Confirm withdrawal {amount} {symbol} to address {address}", "transferConfirm": "Confirm transfer {amount} {symbol} from {from} to {to}", "paymentConfirm": "Confirm deposit ${amount} using {method}"}, "time": {"today": "Today", "yesterday": "Yesterday", "hoursAgo": "{hours} hours ago", "minutesAgo": "{minutes} minutes ago"}, "mockUsers": {"daoren": "<PERSON><PERSON><PERSON>", "chaobi": "Crypto Rookie", "kunpeng": "Kunpeng Project", "jinfa": "<PERSON><PERSON>", "user1": "<PERSON>", "user3": "<PERSON>", "user4": "<PERSON>", "user5": "Xingzhe"}, "all": "All", "noMore": "No more data."}, "tabbar": {"home": "Home", "market": "Market", "trade": "Trade", "asset": "<PERSON><PERSON>", "mine": "Mine"}, "mine": {"quickActions": "Quick Actions", "clients": "Client Management", "commission": "Commission Statistics", "assetDetail": "Asset Details", "setting": "Settings", "broker": "Broker", "trader": "Regular User", "user": "Regular User", "help": "Help", "helpDetail": {"defaultTitle": "Help Details", "content": "This is the detailed content for \"{title}\". Please add explanations according to actual requirements."}, "mockTrade": "Mock Trade", "mockTradeDetail": {"title": "Mock Trading", "exit": "Exit", "totalAssetValue": "Total Asset Value", "mainCoins": "Main Coins", "search": "Search", "name": "Name", "latestPrice": "Latest Price", "todayChange": "Today's Change"}, "invite": "Invite Friends", "inviteRule": {"title": "Invitation Rewards", "ruleTab": "Invitation Reward Rules", "faqTab": "FAQ", "deadline": "Your friends need to complete the following operations before <b>July 9th at 12:00 PM</b>", "step1": "Register through your invitation link or invitation code", "step2": "Complete identity verification", "step3": "Make a deposit worth 100 USDT or more.", "myReward": "My Rewards", "myRewardDesc": "In each event, you can receive up to 3 rewards for inviting friends.\nThere is no limit to the number of friends you can invite", "friendReward": "Friend <PERSON><PERSON>", "friendRewardDesc": "Invited friends will have the opportunity to receive up to 100 USDT worth of newcomer rewards after completing tasks.", "terms": "Event Terms", "faq1": {"q": "How to claim invitation rewards?", "a": "After completing the invitation task, rewards will be automatically sent to your account."}, "faq2": {"q": "What if friend's deposit doesn't meet requirements?", "a": "Only deposits of 100 USDT or more by friends count as meeting requirements."}, "faq3": {"q": "How many friends can I invite?", "a": "There's no limit to the number of friends you can invite, but you can receive up to 3 rewards per event."}}, "inviteRuleTerms": {"title": "Event Terms", "content": "This is the event terms content. Please add detailed terms and conditions according to actual requirements."}, "reward": "My Rewards", "rewardDetail": {"title": "My Rewards", "subtitle": "Earn more rewards and enjoy more benefits", "totalIncentive": "Total Incentive", "totalIncentiveDesc": "Total incentive earned through inviting friends", "level1Incentive": "Level 1 Incentive", "level1IncentiveDesc": "Incentive earned through your direct friend invitations", "level2Incentive": "Level 2 Incentive", "level2IncentiveDesc": "Incentive earned through level 1 friends inviting friends", "level3Incentive": "Level 3 Incentive", "level3IncentiveDesc": "Incentive earned through level 2 friends inviting friends", "myBenefits": "My Benefits", "brokerLevel": "My Broker Level", "level1Cashback": "Level 1 Cashback Rate"}}, "registerSuccess": {"title": "Congratulations!", "subtitle": "Your account registration is successful!", "startJourney": "Start Journey"}, "welcome": {"title": "New Journey<br/>Starts with OKX", "subtitle": "Start your digital asset journey", "time24h": "24H"}, "verifyCode": {"title": "Verify Your Email", "subtitle": "We have sent a verification code to {email}", "errorMessage": "Verification code is incorrect, please re-enter", "countdown": "Resend available in {seconds} seconds", "resend": "Resend Code", "verify": "Verify", "help": {"title": "Didn't receive the code?", "checkSpam": "Please check your spam folder", "checkEmail": "Confirm the email address is correct", "needHelp": "Need help?"}}, "asset": {"title": "<PERSON><PERSON>", "accountAsset": "Account <PERSON><PERSON>", "verified": "Verified", "actions": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "transfer": "Transfer"}, "tabs": {"info": "Account Info", "detail": "Account Details"}, "info": {"availableAsset": "Available Asset (USDT)", "depositMethod": "Deposit Method", "depositAccount": "Depo<PERSON>t Account", "giftAccount": "Gift Account", "totalAmount": "Total Amount", "assetOverview": "Asset Overview", "used": "Used", "available": "Available Asset", "frozen": "<PERSON><PERSON><PERSON>"}, "detail": {"allTypes": "All Types", "timeFilter": "Time Filter", "time": "Time", "type": "Type", "amount": "Amount", "coin": "Coin", "loading": "Loading...", "empty": "No data", "types": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "transfer": "Transfer", "deduct": "Trade Deduct", "income": "Platform Income", "gift": "Platform Gift"}}, "searchPlaceholder": "Enter coin/name"}, "trade": {"userType": "Regular User", "accountType": {"mock": "Demo Account", "real": "Real Account"}, "chartTabs": ["1D", "3H", "1H", "15m", "10m"], "tradeInfo": {"time": "Time", "amount": "Amount", "return": "Return", "profit": "Profit"}, "buttons": {"rise": "Rise", "fall": "Fall"}, "orders": {"unfinished": "Unfinished Orders", "finished": "Finished Orders", "date": "Order Date", "asset": "<PERSON><PERSON>", "endTime": "End Time", "rate": "Profit", "remainTime": "Remaining Time", "buyIndex": "Buy Index", "currentIndex": "Current Index", "expectedProfit": "Expected Profit"}, "timeOptions": {"30s": "30s", "1m": "1m", "5m": "5m", "15m": "15m", "30m": "30m", "1h": "1h"}}, "market": {"searchPlaceholder": "Tesla Current Hot", "tabs": {"market": "Market", "dynamic": "Dynamic", "rank": "Rank"}, "cards": {"totalValue": "Total Value", "dailyVolume": "Daily Volume", "marketShare": "Market Share", "bitcoin": "Bitcoin"}, "notice": "Binary options service will be officially released on August 30", "subTabs": {"follow": "Follow", "stock": "Stock", "bulk": "Bulk", "option": "Option", "overview": "Overview"}, "filters": ["All", "Category 1", "Category 2", "Category 3", "Category 4"], "assetType": {"otc": "OTC"}, "asset": "<PERSON><PERSON>", "dailyLow": "Daily Low", "yieldRate": "Yield Rate", "hotness": "Hotness", "volatility": "Volatility", "dynamic": {"recommend": "Recommend", "newsflash": "Newsflash", "hotNews": "Hot News", "hotTopics": ["Circle's total market cap exceeds USDC", "Trader Eugene: BTC has held $100k, market panic may have peaked", "Female Buffett CRCL made 100M and sold, now shorting Circle?"]}, "rank": {"rate": "Yield Rate", "amount": "Profit <PERSON>", "asset": "<PERSON><PERSON>", "all": "All", "yield": "Yield Rate", "profit": "Profit <PERSON>", "assets": "<PERSON><PERSON>"}}, "dynamic": {"title": "Dynamic", "searchPlaceholder": "SOL Hot Trading", "tabs": {"market": "Market", "dynamic": "Dynamic", "rank": "Rank"}, "subTabs": {"recommend": "Recommend", "newsflash": "Newsflash"}, "hotNews": "Hot News", "hotTopics": ["Circle's total market cap exceeds USDC", "Trader Eugene: BTC has held $100k, market panic may have peaked", "Female Buffett CRCL made 100M and sold, now shorting Circle?"], "feedLinks": ["Connect Wallet", "Connect Social", "<PERSON>lick Discord", "More"], "noFeeds": "No dynamic content"}, "rank": {"title": "Rankings", "subtitle": "Top traders and their performance", "searchPlaceholder": "OKB Current Hot Search", "tabs": {"market": "Market", "dynamic": "Dynamic", "rank": "Rank"}, "subTabs": {"rate": "Yield Rate", "amount": "Profit <PERSON>", "asset": "<PERSON><PERSON>"}, "filter": "All", "labels": {"rate": "Yield Rate", "amount": "Profit <PERSON>", "asset": "<PERSON><PERSON>"}}, "register": {"title": "Create Account", "subtitle": "Start your digital asset journey", "email": "Email Address", "emailPlaceholder": "Enter your email", "password": "Password", "passwordPlaceholder": "Set your password", "confirm": "Confirm Password", "confirmPlaceholder": "Enter password again", "createAccount": "Create Account", "or": "OR", "hasAccount": "Already have an account?", "loginNow": "Sign in now", "emailRequired": "Please enter email address", "emailInvalid": "Please enter a valid email address", "emailTooLong": "Email address too long", "passwordRequired": "Please enter password", "passwordLength": "Password must be at least 8 characters", "passwordTooLong": "Password cannot exceed 50 characters", "passwordNoLetter": "Password must contain letters", "passwordNoNumber": "Password must contain numbers", "confirmRequired": "Please confirm password", "confirmMismatch": "Passwords do not match", "strength": {"weak": "Weak", "normal": "Normal", "medium": "Medium", "strong": "Strong"}}, "payment": {"title": "<PERSON><PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "amountPlaceholder": "Enter deposit amount", "method": "Deposit Method", "bankCard": "Bank Card", "alipay": "Alipay", "wechat": "WeChat Pay", "applePay": "Apple Pay", "googlePay": "Google Pay", "paypal": "PayPal", "confirm": "Confirm <PERSON>", "amountRequired": "Please enter deposit amount", "amountInvalid": "Please enter a valid amount", "methodRequired": "Please select deposit method", "creditCard": "Credit Card", "selectPaymentMethod": "Select Payment Method", "confirmPayment": "Confirm Payment", "processing": "Processing..."}, "withdraw": {"title": "Withdraw", "selectCurrency": "Select Currency", "available": "Available: ", "withdrawAmount": "Withdraw Amount", "amountPlaceholder": "Enter withdraw amount", "fee": "Fee: ", "actualAmount": "Actual Amount: ", "maxWithdraw": "Withdraw All", "withdrawAddress": "Withdraw Address", "addressPlaceholder": "Enter {currency} address", "scan": "<PERSON><PERSON>", "securityNotice": "Security Notice", "notice1": "Please carefully verify the withdrawal address, withdrawals cannot be cancelled", "notice2": "Please ensure the withdrawal address supports the selected currency, otherwise assets may be lost", "notice3": "Current minimum single withdrawal amount: ", "processing": "Processing...", "confirmWithdraw": "Confirm Withdraw"}, "transfer": {"title": "Transfer", "from": "From", "to": "To", "selectCurrency": "Select Currency", "available": "Available: ", "transferAmount": "Transfer Amount", "amountPlaceholder": "Enter transfer amount", "availableBalance": "Available Balance: ", "maxTransfer": "Transfer All", "processing": "Processing...", "confirmTransfer": "Confirm Transfer", "accounts": {"spot": "Spot Account", "futures": "Futures Account", "margin": "<PERSON><PERSON> Account"}}, "setting": {"title": "Settings", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "theme": "Theme Mode", "color": "Color Settings", "time": "Time Zone", "indicator": "Market Indicators", "haptic": "Haptic <PERSON>", "upColor": "Up Color", "downColor": "Down Color", "account": "Account <PERSON><PERSON>", "security": "Security Settings", "notification": "Notification Settings", "about": "About Us", "logout": "Logout", "options": {"light": "Light", "dark": "Dark", "system": "System", "volume": "Volume", "macd": "MACD", "rsi": "RSI", "kdj": "KDJ"}}, "inviteList": {"title": "My Invitations", "stats": "You have invited <span class=\"highlight\">{count}</span> friends & earned <span class=\"highlight\">{reward} USDT</span> {gift}", "continueInvite": "Continue inviting friends to unlock more benefits!", "noRecords": "No invitation records", "emptySubtext": "Invite friends to join and earn rewards", "refreshing": "Refreshing...", "pullToRefresh": "Pull to refresh", "regularUser": "Regular User", "shareInviteLink": "Share Invite Link", "shareTitle": "Invite Friends to Join", "shareText": "Register using my invitation link and we'll both get rewards!", "shareSuccess": "Share successful", "copySuccess": "Invitation link copied to clipboard", "copyError": "Co<PERSON> failed, please copy the link manually", "refreshSuccess": "Refresh successful", "refreshError": "Refresh failed, please try again later", "searchDeveloping": "Search feature under development", "searchPlaceholder": "Search by name, nickname or account..."}, "invite": {"warning": "According to platform requirements, you need to complete identity verification before claiming rewards.", "authBtn": "Verify →", "title": "Invite one friend to get up to {amount} reward", "subtitle": "Ends in 5 days", "description": "For each friend who completes registration and deposits 100 USDT in a single transaction, you will receive up to 10 USDT worth of BTC rewards.", "ruleLink": "Invitation Reward Rules", "buttons": {"telegram": "Telegram Friends", "inviteCode": "Invite Code", "inviteLink": "Invite Link"}, "modal": {"qrCode": "Invite Code QR Code", "qrLink": "Invite Link QR Code", "inviteCode": "Invite Code: {code}", "copyCode": "Copy Invite Code", "copyLink": "Copy Link", "copied": "<PERSON>pied", "close": "Close"}, "noRecords": "No invite records", "emptySubtext": "Invite friends to join and earn rewards", "continueInvite": "Continue inviting friends to unlock more benefits!", "noInvite": "No invitees yet."}, "profile": {"title": "Profile", "uid": "UID: {uid}", "fields": {"nickname": "Nickname", "uid": "UID", "gender": "Gender", "birthday": "Birthday", "realName": "Real Name", "vipLevel": "VIP Level", "phone": "Phone", "email": "Email", "registerTime": "Register Time", "signature": "Signature"}, "genderOptions": {"male": "Male", "female": "Female", "secret": "Secret"}, "status": {"verified": "Verified", "unverified": "Unverified"}, "actions": {"save": "Save", "saving": "Saving...", "cancel": "Cancel"}, "success": {"updated": "Profile updated successfully!", "avatarUpdated": "Avatar updated successfully!"}, "errors": {"updateFailed": "Failed to update profile.", "uploadFailed": "Failed to upload image."}, "validation": {"invalidPhone": "Invalid phone number format.", "invalidImageType": "Please select a valid image file.", "imageTooLarge": "Image size must not exceed 5MB."}}, "mockTrade": {"title": "Mock Trading", "exit": "Exit", "totalAssetValue": "Total Asset Value", "mainCoins": "Main Coins", "search": "Search coin/name", "name": "Coin", "latestPrice": "Latest Price", "todayChange": "Today's Change"}, "inviteRule": {"title": "Invitation Rewards", "ruleTab": "Invitation Reward Rules", "faqTab": "FAQ", "deadline": "Your friends need to complete the following operations before <b>July 9th at 12:00 PM</b>", "step1": "Register through your invitation link or invitation code", "step2": "Complete identity verification", "step3": "Make a deposit worth 100 USDT or more.", "myReward": "My Rewards", "myRewardDesc": "In each event, you can receive up to 3 rewards for inviting friends.\nThere is no limit to the number of friends you can invite", "friendReward": "Friend <PERSON><PERSON>", "friendRewardDesc": "Invited friends will have the opportunity to receive up to 100 USDT worth of newcomer rewards after completing tasks.", "terms": "Event Terms", "faq1": {"q": "How to claim invitation rewards?", "a": "After completing the invitation task, rewards will be automatically sent to your account."}, "faq2": {"q": "What if friend's deposit doesn't meet requirements?", "a": "Only deposits of 100 USDT or more by friends count as meeting requirements."}, "faq3": {"q": "How many friends can I invite?", "a": "There's no limit to the number of friends you can invite, but you can receive up to 3 rewards per event."}}, "help": {"title": "Having problems?<br/>We're here to help", "searchPlaceholder": "Please enter your question", "hotTopics": "Hot Topics", "viewAll": "View All", "hotArticles": "Hot Articles", "topics": {"inviteFriends": "Invite Friends and Node Plan", "accountManagement": "Account Management and App Updates", "verification": "Additional Verification and Info Updates", "depositWithdraw": "Exchange Deposit/Withdrawal Issues", "contractTrading": "Contract Trading Fees and Rules", "web3Wallet": "Web3 Wallet FAQ", "c2cDispute": "C2C Disputes and T+N Security Protection"}}}