import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import './styles/global.scss'
import './styles/components.scss'
import { initTheme } from './utils/theme'

// 初始化主题
initTheme()
import { createI18n } from 'vue-i18n'
import Toast from 'vue-toastification'
import 'vue-toastification/dist/index.css'
import zhCN from './locales/zh-CN.json'
import enUS from './locales/en-US.json'
import jaJP from './locales/ja-JP.json'
import koKR from './locales/ko-KR.json'
import zhTW from './locales/zh-TW.json'
import VueECharts from 'vue-echarts'
import * as echarts from 'echarts'
import { touchFeedback } from './directives/touchFeedback'
import CommonComponents from './components/common'

const messages = {
  'zh-CN': zhCN,
  'en-US': enUS,
  'ja-JP': jaJP,
  'ko-KR': koKR,
  'zh-TW': zhTW
}

// 兼容旧版存储的zh_CN/en_US格式
const storedLocale = localStorage.getItem('language')
const locale = storedLocale 
  ? storedLocale.replace('_', '-')
  : 'zh-CN'

const i18n = createI18n({
  legacy: false,
  locale,
  fallbackLocale: 'zh-CN',
  messages
})

const app = createApp(App)
app.use(router)
app.use(createPinia())
app.use(i18n)
app.use(Toast, {
  transition: "Vue-Toastification__bounce",
  maxToasts: 3,
  newestOnTop: true,
  position: "top-right",
  timeout: 3000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: "button",
  icon: true,
  rtl: false
})
// 挂载Toast到window，便于全局调用
window.$toast = app.config.globalProperties.$toast
app.component('v-chart', VueECharts)
app.directive('touch-feedback', touchFeedback)
app.use(CommonComponents)
app.mount('#app') 