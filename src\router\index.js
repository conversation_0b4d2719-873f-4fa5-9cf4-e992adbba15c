import { createRouter, createWebHistory } from 'vue-router'
import { ref } from 'vue'
import Welcome from '../views/Welcome.vue'
import Home from '../views/Home.vue'
import Login from '../views/Login.vue'
import Register from '../views/Register.vue'
import VerifyCode from '../views/VerifyCode.vue'
import RegisterSuccess from '../views/RegisterSuccess.vue'
import Market from '../views/Market.vue'
import Trade from '../views/Trade.vue'
import Asset from '../views/Asset.vue'
import Payment from '../views/Payment.vue'
import Withdraw from '../views/Withdraw.vue'
import Transfer from '../views/Transfer.vue'
import Dynamic from '../views/Dynamic.vue'
import Rank from '../views/Rank.vue'
import Setting from '../views/Setting.vue'
import Profile from '../views/Profile.vue'
import Help from '../views/Help.vue'
import Invite from '../views/Invite.vue'
import InviteRule from '../views/InviteRule.vue'
import InviteRuleTerms from '../views/InviteRuleTerms.vue'
import MockTrade from '../views/MockTrade.vue'
import HelpDetail from '../views/HelpDetail.vue'
import ArticleList from '../views/ArticleList.vue'
import ArticleDetail from '../views/ArticleDetail.vue'

const routes = [
  { path: '/', redirect: '/home' },
  { path: '/welcome', component: Welcome },
  { path: '/home', component: Home },
  { path: '/login', component: Login },
  { path: '/forgot-password', component: () => import('../views/ForgotPassword.vue') },
  { path: '/register', component: Register },
  { path: '/verify', component: VerifyCode },
  { path: '/register-success', component: RegisterSuccess },
  { path: '/market', component: Market },
  { path: '/trade', component: Trade },
  { path: '/asset', component: Asset },
  { path: '/payment', component: Payment },
  { path: '/withdraw', component: Withdraw },
  { path: '/transfer', component: Transfer },
  { path: '/dynamic', component: Dynamic },
  { path: '/rank', component: Rank },
  { path: '/setting', component: Setting },
  { path: '/profile', component: Profile },
  { path: '/help', component: Help },
  { path: '/help-detail', component: HelpDetail },
  { path: '/invite', component: Invite },
  { path: '/invite-rule', component: InviteRule },
  { path: '/invite-rule-terms', component: InviteRuleTerms },
  { path: '/mock-trade', component: MockTrade },
  { path: '/article-list', component: () => import('../views/ArticleList.vue') },
  { path: '/article-detail', component: () => import('../views/ArticleDetail.vue') },
  {
    path: '/invite-list',
    name: 'InviteList',
    component: () => import('../views/InviteList.vue')
  },
  {
    path: '/mine',
    name: 'Mine',
    component: () => import('../views/Mine.vue')
  },
  {
    path: '/reward',
    name: 'Reward',
    component: () => import('../views/Reward.vue')
  }
]

// 创建导航方向状态
export const navigationDirection = ref('forward')

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 添加导航守卫来跟踪导航方向
let historyState = window.history.state?.position || 0

router.beforeEach((to, from) => {
  // 获取当前历史状态位置
  const toHistoryState = window.history.state?.position || 0
  
  // 根据历史状态位置判断导航方向
  navigationDirection.value = toHistoryState > historyState ? 'forward' : 'backward'
  
  // 更新历史状态位置
  historyState = toHistoryState
  
  return true
})

export default router 