import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUserStore = defineStore('user', () => {
  const user = ref(null)
  const token = ref(null)
  const rememberMe = ref(false)

  const setUser = (userData) => {
    user.value = userData
    if (userData?.token) {
      token.value = userData.token
    }
  }

  const setToken = (newToken) => {
    token.value = newToken
  }

  const setRememberMe = (value) => {
    rememberMe.value = value
  }

  const clearUser = () => {
    user.value = null
    token.value = null
    rememberMe.value = false
  }

  return {
    user,
    token,
    rememberMe,
    setUser,
    setToken,
    setRememberMe,
    clearUser
  }
})