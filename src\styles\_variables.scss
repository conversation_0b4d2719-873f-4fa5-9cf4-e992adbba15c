@use 'sass:color';
@use 'sass:map';
// ===== 颜色系统 =====
// 主色调
$primary: #000;
$secondary: #fff;
$accent: #ffb800;
$success: #00c853;
$danger: #ff5252;
$warning: #ff9800;
$info: #2196f3;

// 灰度系统
$gray-50: #fafafa;
$gray-100: #f5f5f5;
$gray-200: #eeeeee;
$gray-300: #e0e0e0;
$gray-400: #bdbdbd;
$gray-500: #9e9e9e;
$gray-600: #757575;
$gray-700: #616161;
$gray-800: #424242;
$gray-900: #212121;

// 背景色
$bg-primary: $gray-100;
$bg-secondary: $secondary;
$bg-card: $secondary;
$bg-overlay: rgba(0, 0, 0, 0.5);

// 文字颜色
$text-primary: $primary;
$text-secondary: $gray-600;
$text-muted: $gray-500;
$text-inverse: $secondary;

// ===== 字体系统 =====
$font-main: 'Helvetica Neue', Arial, sans-serif;
$font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 30px;
$font-size-4xl: 36px;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// ===== 间距系统 =====
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-2xl: 48px;
$spacing-3xl: 64px;

// ===== 圆角系统 =====
$radius-sm: 4px;
$radius-md: 8px;
$radius-lg: 12px;
$radius-xl: 16px;
$radius-2xl: 24px;
$radius-full: 9999px;
$border-radius-full: 9999px;

// ===== 阴影系统 =====
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// ===== 动画系统 =====
$transition-fast: 0.15s ease-in-out;
$transition-normal: 0.3s ease-in-out;
$transition-slow: 0.5s ease-in-out;

// ===== 断点系统 =====
$breakpoint-sm: 480px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;

// 新的断点系统 - 使用Map便于混合宏使用
$breakpoints: (
  'xs': 375px,   // 小型手机
  'sm': 576px,   // 大型手机
  'md': 768px,   // 平板竖屏
  'lg': 1024px,  // 平板横屏/小型笔记本
  'xl': 1366px,  // 大型笔记本
  'xxl': 1920px  // 桌面显示器
);

// 响应式混合宏
@mixin respond-to($breakpoint) {
  $value: map.get($breakpoints, $breakpoint);
  @if $value {
    @media screen and (min-width: $value) {
      @content;
    }
  }
}

@mixin respond-below($breakpoint) {
  $value: map.get($breakpoints, $breakpoint);
  @if $value {
    @media screen and (max-width: $value - 1) {
      @content;
    }
  }
}

// ===== 组件变量 =====
// 按钮
$btn-padding-y: 12px;
$btn-padding-x: 24px;
$btn-font-size: $font-size-base;
$btn-border-radius: $radius-lg;
$btn-transition: $transition-normal;

// 卡片
$card-padding: $spacing-lg;
$card-border-radius: $radius-xl;
$card-shadow: $shadow-md;
$card-bg: $bg-card;

// 输入框
$input-padding-y: 12px;
$input-padding-x: 16px;
$input-border-radius: $radius-md;
$input-border-color: $gray-300;
$input-focus-border-color: $accent;

// 标签页
$tab-padding-y: 12px;
$tab-padding-x: 16px;
$tab-active-color: $accent;
$tab-inactive-color: $text-secondary;

// 表格
$table-border-color: $gray-200;
$table-stripe-bg: $gray-50;
$table-hover-bg: $gray-100;

// 加载状态
$loading-spinner-size: 24px;
$loading-spinner-color: $accent;

// ===== 布局变量 =====
$container-max-width: 1200px;
$sidebar-width: 280px;
$header-height: 60px;
$footer-height: 80px;
$tab-bar-height: 56px;
$z-index-tab-bar: 100;

// ===== 组件变量 =====
// 交易按钮
$trade-icon-size: 48px;
$scale-active: 0.95;

// ===== 过渡动画 =====
$transition-color: 0.2s ease;
$transition-stroke: 0.2s ease;
$transition-transform: 0.2s ease;

// ===== 间距扩展 =====
$spacing-xxs: 2px;
$spacing-xxxs: 1px;

// ===== 设备检测相关变量 =====
$safe-area-inset-top: env(safe-area-inset-top, 0px);
$safe-area-inset-right: env(safe-area-inset-right, 0px);
$safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
$safe-area-inset-left: env(safe-area-inset-left, 0px);

// 设备类型特定变量
$mobile-header-height: 48px;
$mobile-tab-bar-height: 50px;
$tablet-header-height: 52px;
$tablet-tab-bar-height: 52px;
$desktop-header-height: 60px;
$desktop-tab-bar-height: 56px;

// 方向特定变量
$portrait-content-padding: $spacing-md;
$landscape-content-padding: $spacing-lg;
$landscape-tab-bar-height: 48px;

// ===== 旧变量兼容 =====
$gray: $gray-100;
$radius: $radius-xl; 
$success-light: color.mix(white, $success, 40%);
$primary-dark: color.mix(black, $primary, 10%);
$shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-up-lg: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);