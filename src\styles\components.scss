@use './_variables.scss' as *;
@use 'sass:color';

// ===== 页面通用样式 =====
.page-container {
  min-height: 100vh;
  background: $bg-primary;
  padding-bottom: 80px; // 为底部导航留出空间
}

// ===== 顶部导航栏 =====
.topbar {
  background: $primary;
  color: $text-inverse;
  padding: $spacing-md $spacing-lg;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  
  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: $container-max-width;
    margin: 0 auto;
  }
  
  &__title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
  }
  
  &__actions {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
  }
  
  &__btn {
    background: transparent;
    border: none;
    color: $text-inverse;
    padding: $spacing-sm;
    border-radius: $radius-md;
    cursor: pointer;
    transition: all $transition-fast;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

// ===== 资产卡片 =====
.asset-card {
  background: $card-bg;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  padding: $card-padding;
  transition: all $transition-normal;
  margin: $spacing-lg;
  position: relative;
  overflow: hidden;
  
  &:hover {
    box-shadow: $shadow-lg;
    transform: translateY(-2px);
  }
  
  &__header {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    margin-bottom: $spacing-lg;
  }
  
  &__avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
  }
  
  &__title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    
    span {
      color: $text-secondary;
      font-weight: $font-weight-normal;
    }
  }
  
  &__balance {
    font-size: $font-size-3xl;
    font-weight: $font-weight-bold;
    margin-bottom: $spacing-md;
  }
  
  &__id {
    color: $text-secondary;
    font-size: $font-size-sm;
    margin-bottom: $spacing-lg;
    
    .verified-badge {
      background: $success;
      color: $text-inverse;
      padding: 2px $spacing-sm;
      border-radius: $radius-sm;
      font-size: $font-size-xs;
      margin-left: $spacing-sm;
    }
  }
  
  &__actions {
    display: flex;
    gap: $spacing-md;
    
    .btn {
      flex: 1;
      position: relative;
      overflow: hidden;
      
      .btn-icon-wrapper {
        margin-right: $spacing-sm;
      }
      
      .btn-highlight {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }
      
      &:hover .btn-highlight {
        left: 100%;
      }
    }
  }
}

// ===== 标签页 =====
.tabs-container {
  display: flex;
  background: $bg-secondary;
  border-radius: $radius-lg;
  padding: $spacing-sm;
  margin: $spacing-lg;
  box-shadow: $shadow-sm;
  
  span {
    flex: 1;
    text-align: center;
    padding: $spacing-md;
    border-radius: $radius-md;
    cursor: pointer;
    transition: all $transition-fast;
    position: relative;
    font-weight: $font-weight-medium;
    
    &:hover {
      background: $gray-50;
    }
    
    &.active {
      background: $primary;
      color: $text-inverse;
      
      .tab-indicator {
        position: absolute;
        bottom: -$spacing-sm;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 3px;
        background: $accent;
        border-radius: $radius-sm;
      }
    }
  }
}

// ===== 信息卡片 =====
.info-card {
  background: $card-bg;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  padding: $card-padding;
  transition: all $transition-normal;
  margin: $spacing-lg;
  
  &:hover {
    box-shadow: $shadow-lg;
    transform: translateY(-2px);
  }
  
  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-md 0;
    border-bottom: 1px solid $gray-200;
    
    &:last-child {
      border-bottom: none;
    }
    
    .info-label {
      color: $text-secondary;
      font-size: $font-size-sm;
    }
    
    .info-value {
      text-align: right;
      font-weight: $font-weight-medium;
      
      &.blur-text {
        filter: blur(4px);
      }
    }
  }
}

// ===== 资产概览 =====
.asset-summary {
  background: $card-bg;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  padding: $card-padding;
  transition: all $transition-normal;
  margin: $spacing-lg;
  
  &:hover {
    box-shadow: $shadow-lg;
    transform: translateY(-2px);
  }
  
  h3 {
    margin-bottom: $spacing-lg;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
  }
  
  .asset-chart {
    display: flex;
    align-items: center;
    gap: $spacing-xl;
    
    .chart-placeholder {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .chart-center {
      position: absolute;
      text-align: center;
      
      .chart-percent {
        font-size: $font-size-lg;
        font-weight: $font-weight-bold;
      }
      
      .chart-label {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
    
    .chart-legend {
      flex: 1;
      
      .legend-item {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        margin-bottom: $spacing-sm;
        
        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 50%;
        }
        
        .legend-text {
          flex: 1;
          font-size: $font-size-sm;
        }
        
        .legend-value {
          font-weight: $font-weight-medium;
        }
      }
    }
  }
}

// ===== 明细表格 =====
.asset-detail-area {
  margin: $spacing-lg;
  
  .detail-filter {
    display: flex;
    gap: $spacing-md;
    margin-bottom: $spacing-lg;
    
    .filter-btn {
      background: $bg-secondary;
      border: 1px solid $gray-300;
      padding: $spacing-sm $spacing-md;
      border-radius: $radius-md;
      cursor: pointer;
      transition: all $transition-fast;
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      
      &:hover {
        border-color: $accent;
        color: $accent;
      }
    }
  }
  
  .asset-detail-table {
    background: $bg-secondary;
    border-radius: $radius-lg;
    overflow: hidden;
    box-shadow: $shadow-sm;
    
    .asset-detail-header {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      background: $gray-50;
      padding: $spacing-md;
      font-weight: $font-weight-semibold;
      color: $text-secondary;
      font-size: $font-size-sm;
    }
    
    .asset-detail-row {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      padding: $spacing-md;
      border-bottom: 1px solid $gray-200;
      cursor: pointer;
      transition: all $transition-fast;
      
      &:hover {
        background: $gray-50;
      }
      
      &.row-highlight {
        background: rgba($accent, 0.1);
      }
      
      .type-badge {
        display: inline-block;
        padding: 2px $spacing-sm;
        border-radius: $radius-sm;
        font-size: $font-size-xs;
        font-weight: $font-weight-medium;
        
        &.type-deposit {
          background: rgba($success, 0.1);
          color: $success;
        }
        
        &.type-withdraw {
          background: rgba($danger, 0.1);
          color: $danger;
        }
        
        &.type-transfer {
          background: rgba($info, 0.1);
          color: $info;
        }
        
        &.type-deduct {
          background: rgba($warning, 0.1);
          color: $warning;
        }
        
        &.type-income {
          background: rgba($success, 0.1);
          color: $success;
        }
        
        &.type-gift {
          background: rgba($accent, 0.1);
          color: $accent;
        }
      }
      
      .amount-plus {
        color: $success;
      }
      
      .amount-minus {
        color: $danger;
      }
    }
  }
}

// ===== 加载状态 =====
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-2xl;
  
  .loading-spinner {
    width: $loading-spinner-size;
    height: $loading-spinner-size;
    border: 2px solid $gray-200;
    border-top: 2px solid $loading-spinner-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// ===== 空状态 =====
.empty-state {
  text-align: center;
  padding: $spacing-2xl;
  color: $text-secondary;
  
  .empty-icon {
    font-size: $font-size-4xl;
    margin-bottom: $spacing-md;
  }
  
  .empty-text {
    font-size: $font-size-lg;
  }
}

// ===== 表单样式 =====
.form {
  &__group {
    margin-bottom: $spacing-lg;
  }
  
  &__label {
    display: block;
    margin-bottom: $spacing-sm;
    font-weight: $font-weight-medium;
    color: $text-primary;
  }
  
  &__input {
    width: 100%;
    padding: $input-padding-y $input-padding-x;
    font-size: $font-size-base;
    border: 1px solid $input-border-color;
    border-radius: $input-border-radius;
    background: $bg-secondary;
    transition: all $transition-fast;
    
    &:focus {
      outline: none;
      border-color: $input-focus-border-color;
      box-shadow: 0 0 0 3px rgba($accent, 0.1);
    }
    
    &::placeholder {
      color: $text-muted;
    }
    
    &--error {
      border-color: $danger;
      
      &:focus {
        border-color: $danger;
        box-shadow: 0 0 0 3px rgba($danger, 0.1);
      }
    }
  }
  
  &__error {
    color: $danger;
    font-size: $font-size-sm;
    margin-top: $spacing-xs;
  }
  
  &__help {
    color: $text-secondary;
    font-size: $font-size-sm;
    margin-top: $spacing-xs;
  }
}

// ===== 支付方式卡片 =====
.payment-method {
  background: $card-bg;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  padding: $card-padding;
  transition: all $transition-normal;
  display: flex;
  align-items: center;
  gap: $spacing-md;
  cursor: pointer;
  transition: all $transition-fast;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
  
  &__icon {
    width: 40px;
    height: 40px;
    border-radius: $radius-md;
    display: flex;
    align-items: center;
    justify-content: center;
    background: $gray-100;
  }
  
  &__info {
    flex: 1;
    
    .payment-method__name {
      font-weight: $font-weight-medium;
      margin-bottom: 2px;
    }
    
    .payment-method__desc {
      font-size: $font-size-sm;
      color: $text-secondary;
    }
  }
  
  &__radio {
    width: 20px;
    height: 20px;
    border: 2px solid $gray-300;
    border-radius: 50%;
    position: relative;
    
    &.selected {
      border-color: $accent;
      
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 8px;
        height: 8px;
        background: $accent;
        border-radius: 50%;
      }
    }
  }
}

// ===== 银行卡片样式 =====
.card-bank {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-md;
  background: $gray-50;
  border-radius: $radius-md;
  
  &.small {
    padding: $spacing-sm;
    
    .card-bank-logo {
      width: 24px;
      height: 16px;
    }
    
    .card-number {
      font-size: $font-size-sm;
    }
  }
  
  &__logo {
    width: 32px;
    height: 20px;
    border-radius: $radius-sm;
    overflow: hidden;
  }
  
  &__details {
    flex: 1;
    
    .card-number {
      font-weight: $font-weight-medium;
    }
  }
}

// ===== 响应式调整 =====
@media (max-width: $breakpoint-sm) {
  .asset-card,
  .info-card,
  .asset-summary,
  .asset-detail-area {
    margin: $spacing-sm;
  }
  
  .asset-card__actions {
    flex-direction: column;
  }
  
  .asset-chart {
    flex-direction: column;
    gap: $spacing-lg;
  }
  
  .asset-detail-header,
  .asset-detail-row {
    grid-template-columns: 1fr 1fr;
    font-size: $font-size-sm;
    
    span:nth-child(3),
    span:nth-child(4) {
      display: none;
    }
  }
  
  .tabs-container {
    margin: $spacing-sm;
    
    span {
      padding: $spacing-sm;
      font-size: $font-size-sm;
    }
  }
}

@media (max-width: $breakpoint-md) {
  .topbar__content {
    padding: 0 $spacing-md;
  }
  
  .asset-card__balance {
    font-size: $font-size-2xl;
  }
  
  .payment-method {
    padding: $spacing-md;
  }
} 