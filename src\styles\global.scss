@use './variables.scss' as *;
@use 'sass:color';
@use './theme.scss' as *;

// ===== 基础重置 =====
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: $font-main;
  font-size: 16px; // 基准字体大小
  line-height: $line-height-normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  
  @include respond-below('xs') {
    font-size: 14px;
  }
  
  @include respond-to('lg') {
    font-size: 18px;
  }
  
  @include respond-to('xl') {
    font-size: 20px;
  }
}

body {
  font-family: $font-main;
  font-size: $font-size-base;
  line-height: $line-height-normal;
  color: $text-primary;
  background: $bg-primary;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

// CSS变量系统
:root {
  // 基础变量
  --tab-bar-height: 3.5rem;
  --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
  --safe-area-inset-top: env(safe-area-inset-top, 0px);
  --content-padding: 1rem;
  
  // 动态计算变量
  --app-content-bottom: calc(var(--tab-bar-height) + var(--safe-area-inset-bottom));
  
  @media screen and (max-width: 375px) {
    --tab-bar-height: 3rem;
    --content-padding: 0.75rem;
  }
  
  @media screen and (min-width: 1024px) {
    --tab-bar-height: 4rem;
    --content-padding: 1.5rem;
  }
}

// ===== 链接样式 =====
a {
  color: inherit;
  text-decoration: none;
  transition: color $transition-fast;
  
  &:hover {
    color: $accent;
  }
}

// ===== 按钮基础样式 =====
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $btn-padding-y $btn-padding-x;
  font-size: $btn-font-size;
  font-weight: $font-weight-medium;
  border: none;
  border-radius: $btn-border-radius;
  cursor: pointer;
  transition: all $btn-transition;
  text-decoration: none;
  white-space: nowrap;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  // 主要按钮
  &--primary {
    background: $primary;
    color: $text-inverse;
    
    &:hover:not(:disabled) {
      background: $gray-800;
      transform: translateY(-1px);
      box-shadow: $shadow-md;
    }
  }
  
  // 次要按钮
  &--secondary {
    background: $bg-secondary;
    color: $text-primary;
    border: 1px solid $gray-300;
    
    &:hover:not(:disabled) {
      background: $gray-50;
      border-color: $gray-400;
    }
  }
  
  // 强调按钮
  &--accent {
    background: $accent;
    color: $text-primary;
    
    &:hover:not(:disabled) {
      background: color.adjust($accent, $lightness: -10%);
      transform: translateY(-1px);
      box-shadow: $shadow-md;
    }
  }
  
  // 成功按钮
  &--success {
    background: $success;
    color: $text-inverse;
    
    &:hover:not(:disabled) {
      background: color.adjust($success, $lightness: -10%);
    }
  }
  
  // 危险按钮
  &--danger {
    background: $danger;
    color: $text-inverse;
    
    &:hover:not(:disabled) {
      background: color.adjust($danger, $lightness: -10%);
    }
  }
  
  // 小按钮
  &--sm {
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-sm;
  }
  
  // 大按钮
  &--lg {
    padding: $spacing-lg $spacing-xl;
    font-size: $font-size-lg;
  }
}

// ===== 卡片基础样式 =====
.card {
  background: $card-bg;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  padding: $card-padding;
  transition: all $transition-normal;
  
  &:hover {
    box-shadow: $shadow-lg;
    transform: translateY(-2px);
  }
  
  &--compact {
    padding: $spacing-md;
  }
  
  &--elevated {
    box-shadow: $shadow-lg;
  }
}

// ===== 输入框基础样式 =====
.input {
  width: 100%;
  padding: $input-padding-y $input-padding-x;
  font-size: $font-size-base;
  border: 1.5px solid $input-border-color;
  border-radius: $input-border-radius;
  background: $bg-secondary;
  transition: all $transition-fast;
  
  &:focus {
    outline: none;
    border-color: $accent;
    box-shadow: 0 0 0 2px rgba($accent, 0.12);
  }
  
  &::placeholder {
    color: $text-muted;
  }
  
  &--error {
    border-color: $danger;
    
    &:focus {
      border-color: $danger;
      box-shadow: 0 0 0 2px rgba($danger, 0.12);
    }
  }
}

// ===== 标签页基础样式 =====
.tabs {
  display: flex;
  border-bottom: 1px solid $gray-200;
  
  &__item {
    padding: $tab-padding-y $tab-padding-x;
    color: $tab-inactive-color;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all $transition-fast;
    font-weight: $font-weight-medium;
    
    &:hover {
      color: $text-primary;
    }
    
    &--active {
      color: $tab-active-color;
      border-bottom-color: $tab-active-color;
    }
  }
}

// ===== 表格基础样式 =====
.table {
  width: 100%;
  border-collapse: collapse;
  
  &__header {
    background: $gray-50;
    border-bottom: 1px solid $table-border-color;
    
    th {
      padding: $spacing-md;
      text-align: left;
      font-weight: $font-weight-semibold;
      color: $text-secondary;
    }
  }
  
  &__row {
    border-bottom: 1px solid $table-border-color;
    transition: background $transition-fast;
    
    &:hover {
      background: $table-hover-bg;
    }
    
    &:nth-child(even) {
      background: $table-stripe-bg;
    }
    
    td {
      padding: $spacing-md;
    }
  }
}

// ===== 加载状态 =====
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  
  &__spinner {
    width: $loading-spinner-size;
    height: $loading-spinner-size;
    border: 2px solid $gray-200;
    border-top: 2px solid $loading-spinner-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  &__text {
    margin-left: $spacing-md;
    color: $text-secondary;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// ===== 页面布局 =====
.page {
  min-height: 100vh;
  padding-bottom: 80px; // 为底部导航留出空间
  
  &__header {
    background: $bg-secondary;
    border-bottom: 1px solid $gray-200;
    padding: $spacing-md $spacing-lg;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
  }
  
  &__content {
    max-width: $container-max-width;
    margin: 0 auto;
    padding: $spacing-lg;
  }
}

// ===== 装饰元素 =====
.decoration {
  position: fixed;
  pointer-events: none;
  z-index: -1;
  
  &--circle {
    border-radius: 50%;
    background: linear-gradient(45deg, $accent, color.adjust($accent, $lightness: 20%));
    opacity: 0.1;
    
    &-1 {
      width: 200px;
      height: 200px;
      top: -100px;
      right: -100px;
    }
    
    &-2 {
      width: 150px;
      height: 150px;
      bottom: -75px;
      left: -75px;
    }
  }
}

// ===== 动画类 =====
.fade-enter-active, .fade-leave-active {
  transition: opacity $transition-normal;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active, .slide-up-leave-active {
  transition: all $transition-normal;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.scale-enter-active, .scale-leave-active {
  transition: all $transition-normal;
}

.scale-enter-from, .scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(.4,0,.2,1);
}
.fade-slide-enter-from, .fade-slide-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
.fade-slide-enter-to, .fade-slide-leave-from {
  opacity: 1;
  transform: translateY(0);
}

// ===== 工具类 =====
.text {
  &--center { text-align: center; }
  &--left { text-align: left; }
  &--right { text-align: right; }
  
  &--primary { color: $text-primary; }
  &--secondary { color: $text-secondary; }
  &--muted { color: $text-muted; }
  &--success { color: $success; }
  &--danger { color: $danger; }
  &--warning { color: $warning; }
  &--info { color: $info; }
  &--accent { color: $accent; }
  
  &--xs { font-size: $font-size-xs; }
  &--sm { font-size: $font-size-sm; }
  &--base { font-size: $font-size-base; }
  &--lg { font-size: $font-size-lg; }
  &--xl { font-size: $font-size-xl; }
  &--2xl { font-size: $font-size-2xl; }
  &--3xl { font-size: $font-size-3xl; }
  &--4xl { font-size: $font-size-4xl; }
  
  &--light { font-weight: $font-weight-light; }
  &--normal { font-weight: $font-weight-normal; }
  &--medium { font-weight: $font-weight-medium; }
  &--semibold { font-weight: $font-weight-semibold; }
  &--bold { font-weight: $font-weight-bold; }
}

.margin {
  &--xs { margin: $spacing-xs; }
  &--sm { margin: $spacing-sm; }
  &--md { margin: $spacing-md; }
  &--lg { margin: $spacing-lg; }
  &--xl { margin: $spacing-xl; }
  &--2xl { margin: $spacing-2xl; }
  &--3xl { margin: $spacing-3xl; }
  
  &-t--xs { margin-top: $spacing-xs; }
  &-t--sm { margin-top: $spacing-sm; }
  &-t--md { margin-top: $spacing-md; }
  &-t--lg { margin-top: $spacing-lg; }
  &-t--xl { margin-top: $spacing-xl; }
  
  &-b--xs { margin-bottom: $spacing-xs; }
  &-b--sm { margin-bottom: $spacing-sm; }
  &-b--md { margin-bottom: $spacing-md; }
  &-b--lg { margin-bottom: $spacing-lg; }
  &-b--xl { margin-bottom: $spacing-xl; }
}

.padding {
  &--xs { padding: $spacing-xs; }
  &--sm { padding: $spacing-sm; }
  &--md { padding: $spacing-md; }
  &--lg { padding: $spacing-lg; }
  &--xl { padding: $spacing-xl; }
  &--2xl { padding: $spacing-2xl; }
  &--3xl { padding: $spacing-3xl; }
}

.flex {
  display: flex;
  
  &--center {
    align-items: center;
    justify-content: center;
  }
  
  &--between {
    justify-content: space-between;
  }
  
  &--around {
    justify-content: space-around;
  }
  
  &--column {
    flex-direction: column;
  }
  
  &--wrap {
    flex-wrap: wrap;
  }
}

.hidden {
  display: none !important;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

// ===== 响应式设计 =====
@media (max-width: $breakpoint-sm) {
  html, body {
    font-size: $font-size-sm;
  }
  
  .page__content {
    padding: $spacing-md;
  }
  
  .card {
    padding: $spacing-md;
    border-radius: $radius-lg;
  }
  
  .btn {
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-sm;
  }
  
  .tabs__item {
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-sm;
  }
  
  .table__header th,
  .table__row td {
    padding: $spacing-sm;
    font-size: $font-size-sm;
  }
}

@media (max-width: $breakpoint-md) {
  html, body {
    font-size: $font-size-base;
  }
  
  .page__content {
    padding: $spacing-lg;
  }
  
  .card {
    padding: $spacing-lg;
  }
  
  .btn {
    padding: $btn-padding-y $btn-padding-x;
    font-size: $btn-font-size;
  }
}

@media (max-width: $breakpoint-lg) {
  .page__content {
    max-width: 100%;
  }
}

// ===== 深色模式支持 =====
// 使用theme.scss中定义的.dark-theme类来支持深色模式
// 可以通过添加/移除body上的.dark-theme类来切换深色模式

// ===== 可访问性 =====
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// ===== 打印样式 =====
@media print {
  .btn,
  .tabs,
  .decoration {
    display: none !important;
  }
  
  .page {
    padding: 0;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid $gray-300;
  }
}

// ===== 横屏适配优化 =====
@media screen and (orientation: landscape) and (max-height: 600px) {
  :root {
    --tab-bar-height: 2.5rem;
  }
  
  .tab-bar {
    .tab-bar-content {
      height: var(--tab-bar-height);
    }
  }
  
  // 优化内容区域在横屏模式下的显示
  .main-content {
    margin-top: 2rem;
    padding: 0 var(--content-padding);
    
    h1 {
      font-size: 1.5rem;
      margin-bottom: 0.5rem;
    }
    
    .subtitle {
      margin-bottom: 1.5rem;
    }
    
    .btn-group .main-btn {
      padding: 0.75rem 0;
    }
  }
}

// ===== 设备类型和方向适配类 =====
.is-mobile {
  --header-height: #{$mobile-header-height};
  --tab-bar-height: #{$mobile-tab-bar-height};
}

.is-tablet {
  --header-height: #{$tablet-header-height};
  --tab-bar-height: #{$tablet-tab-bar-height};
}

.is-desktop {
  --header-height: #{$desktop-header-height};
  --tab-bar-height: #{$desktop-tab-bar-height};
}

.is-portrait {
  --content-padding: #{$portrait-content-padding};
}

.is-landscape {
  --content-padding: #{$landscape-content-padding};
  --tab-bar-height: #{$landscape-tab-bar-height};
}

// 安全区域适配类
.safe-area {
  &-padding {
    padding-top: var(--safe-area-inset-top);
    padding-right: var(--safe-area-inset-right);
    padding-bottom: var(--safe-area-inset-bottom);
    padding-left: var(--safe-area-inset-left);
  }
  
  &-padding-top {
    padding-top: var(--safe-area-inset-top);
  }
  
  &-padding-right {
    padding-right: var(--safe-area-inset-right);
  }
  
  &-padding-bottom {
    padding-bottom: var(--safe-area-inset-bottom);
  }
  
  &-padding-left {
    padding-left: var(--safe-area-inset-left);
  }
  
  &-margin {
    margin-top: var(--safe-area-inset-top);
    margin-right: var(--safe-area-inset-right);
    margin-bottom: var(--safe-area-inset-bottom);
    margin-left: var(--safe-area-inset-left);
  }
  
  &-margin-top {
    margin-top: var(--safe-area-inset-top);
  }
  
  &-margin-right {
    margin-right: var(--safe-area-inset-right);
  }
  
  &-margin-bottom {
    margin-bottom: var(--safe-area-inset-bottom);
  }
  
  &-margin-left {
    margin-left: var(--safe-area-inset-left);
  }
}

// 设备可见性控制
.visible-mobile,
.visible-tablet,
.visible-desktop,
.visible-portrait,
.visible-landscape {
  display: none;
}

.is-mobile .visible-mobile {
  display: block;
}

.is-tablet .visible-tablet {
  display: block;
}

.is-desktop .visible-desktop {
  display: block;
}

.is-portrait .visible-portrait {
  display: block;
}

.is-landscape .visible-landscape {
  display: block;
}

.is-mobile .hidden-mobile {
  display: none !important;
}

.is-tablet .hidden-tablet {
  display: none !important;
}

.is-desktop .hidden-desktop {
  display: none !important;
}

.is-portrait .hidden-portrait {
  display: none !important;
}

.is-landscape .hidden-landscape {
  display: none !important;
}

// ===== END ===== 

@mixin ripple-effect {
  position: relative;
  overflow: hidden;
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(0, 0, 0, 0.08);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
    pointer-events: none;
  }
  &:active::after {
    animation: ripple 0.6s ease-out;
  }
}

@keyframes ripple {
  0% { transform: scale(0, 0); opacity: 0.5; }
  20% { transform: scale(25, 25); opacity: 0.3; }
  100% { opacity: 0; transform: scale(40, 40); }
}

.btn, .card {
  @include ripple-effect;
} 