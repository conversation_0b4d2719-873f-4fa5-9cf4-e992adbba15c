@use 'sass:map';
@use 'sass:color';
@use './_variables.scss' as *;

/* 将SCSS变量转换为CSS变量 */
:root {
  /* 主色调 */
  --primary-color: #{$primary};
  --secondary-color: #{$secondary};
  --accent-color: #{$accent};
  --success-color: #{$success};
  --danger-color: #{$danger};
  --warning-color: #{$warning};
  --info-color: #{$info};
  
  /* 灰度系统 */
  --gray-50: #{$gray-50};
  --gray-100: #{$gray-100};
  --gray-200: #{$gray-200};
  --gray-300: #{$gray-300};
  --gray-400: #{$gray-400};
  --gray-500: #{$gray-500};
  --gray-600: #{$gray-600};
  --gray-700: #{$gray-700};
  --gray-800: #{$gray-800};
  --gray-900: #{$gray-900};
  
  /* 背景色 */
  --background-color: #{$bg-primary};
  --background-color-light: #{$gray-50};
  --card-background: #{$bg-card};
  --overlay-background: #{$bg-overlay};
  
  /* 文字颜色 */
  --text-primary: #{$text-primary};
  --text-secondary: #{$text-secondary};
  --text-muted: #{$text-muted};
  --text-inverse: #{$text-inverse};
  
  /* 字体 */
  --font-family: #{$font-main};
  --font-mono: #{$font-mono};
  
  /* 字体大小 */
  --font-size-xs: #{$font-size-xs};
  --font-size-sm: #{$font-size-sm};
  --font-size-md: #{$font-size-base};
  --font-size-lg: #{$font-size-lg};
  --font-size-xl: #{$font-size-xl};
  --font-size-2xl: #{$font-size-2xl};
  --font-size-3xl: #{$font-size-3xl};
  --font-size-4xl: #{$font-size-4xl};
  
  /* 字体粗细 */
  --font-weight-light: #{$font-weight-light};
  --font-weight-normal: #{$font-weight-normal};
  --font-weight-medium: #{$font-weight-medium};
  --font-weight-semibold: #{$font-weight-semibold};
  --font-weight-bold: #{$font-weight-bold};
  
  /* 行高 */
  --line-height-tight: #{$line-height-tight};
  --line-height-normal: #{$line-height-normal};
  --line-height-loose: #{$line-height-relaxed};
  
  /* 间距 */
  --spacing-xxxs: #{$spacing-xxxs};
  --spacing-xxs: #{$spacing-xxs};
  --spacing-xs: #{$spacing-xs};
  --spacing-sm: #{$spacing-sm};
  --spacing-md: #{$spacing-md};
  --spacing-lg: #{$spacing-lg};
  --spacing-xl: #{$spacing-xl};
  --spacing-2xl: #{$spacing-2xl};
  --spacing-3xl: #{$spacing-3xl};
  
  /* 圆角 */
  --radius-sm: #{$radius-sm};
  --radius-md: #{$radius-md};
  --radius-lg: #{$radius-lg};
  --radius-xl: #{$radius-xl};
  --radius-2xl: #{$radius-2xl};
  --radius-round: #{$radius-full};
  
  /* 阴影 */
  --shadow-sm: #{$shadow-sm};
  --shadow-md: #{$shadow-md};
  --shadow-lg: #{$shadow-lg};
  --shadow-xl: #{$shadow-xl};
  
  /* 动画 */
  --animation-duration-fast: #{$transition-fast};
  --animation-duration: #{$transition-normal};
  --animation-duration-slow: #{$transition-slow};
  --animation-timing-function: ease-in-out;
  
  /* 组件特定 */
  --navbar-height: #{$mobile-header-height};
  --tabbar-height: #{$mobile-tab-bar-height};
  
  /* 边框颜色 */
  --border-color: #{$gray-300};
  --border-light: #{$gray-200};
  --border-dark: #{$gray-400};
}

/* 暗黑模式 */
.dark-theme {
  /* 主色调 - 暗黑模式下调整亮度 */
    --primary-color: #{color.scale($primary, $lightness: 10%)};
    --secondary-color: #{color.scale($secondary, $lightness: -80%)};
    --accent-color: #{color.scale($accent, $lightness: 5%)};
    --success-color: #{color.scale($success, $lightness: 5%)};
    --danger-color: #{color.scale($danger, $lightness: 5%)};
    --warning-color: #{color.scale($warning, $lightness: 5%)};
    --info-color: #{color.scale($info, $lightness: 5%)};
  
  /* 灰度系统 - 反转 */
  --gray-50: #{$gray-900};
  --gray-100: #{$gray-800};
  --gray-200: #{$gray-700};
  --gray-300: #{$gray-600};
  --gray-400: #{$gray-500};
  --gray-500: #{$gray-400};
  --gray-600: #{$gray-300};
  --gray-700: #{$gray-200};
  --gray-800: #{$gray-100};
  --gray-900: #{$gray-50};
  
  /* 背景色 */
  --background-color: #121212;
  --background-color-light: #1e1e1e;
  --card-background: #1c1c1e;
  --overlay-background: rgba(0, 0, 0, 0.7);
  
  /* 文字颜色 */
  --text-primary: #e5eaf3;
  --text-secondary: #cfd3dc;
  --text-muted: #a3a6ad;
  --text-inverse: #121212;
  
  /* 边框颜色 */
  --border-color: #303030;
  --border-light: #3a3a3c;
  --border-dark: #232324;
  
  /* 阴影 - 暗黑模式下更强烈 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.5);
}

/* 媒体查询适配不同设备 */
@media screen and (min-width: map.get($breakpoints, 'md')) {
  :root {
    --navbar-height: #{$tablet-header-height};
    --tabbar-height: #{$tablet-tab-bar-height};
  }
}

@media screen and (min-width: map.get($breakpoints, 'lg')) {
  :root {
    --navbar-height: #{$desktop-header-height};
    --tabbar-height: #{$desktop-tab-bar-height};
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) {
  :root {
    --tabbar-height: #{$landscape-tab-bar-height};
  }
}