import router from '@/router'

export function isLoggedIn() {
  return !!localStorage.getItem('token')
}

export function redirectToLogin() {
  // 保存当前路由路径
  const currentPath = router.currentRoute.value.fullPath
  // 排除登录页本身
  if (!currentPath.startsWith('/login')) {
    localStorage.setItem('redirectFrom', currentPath)
  }
  // 弹出未登录提醒
  if (typeof window !== 'undefined' && window.$toast) {
    window.$toast.warning('请先登录')
  } else if (window && window.Toast && typeof window.Toast === 'function') {
    window.Toast('请先登录')
  }
  // 跳转到登录页
  router.push('/login')
}