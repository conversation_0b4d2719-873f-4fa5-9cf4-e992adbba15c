import router from '@/router'

export function isLoggedIn() {
  // 检查token是否存在且未过期
  const token = localStorage.getItem('token');
  const tokenData = localStorage.getItem('tokenData');
  
  if (!token) {
    return false;
  }
  
  // 如果有tokenData，检查是否过期
  if (tokenData) {
    try {
      const parsedTokenData = JSON.parse(tokenData);
      if (parsedTokenData.expiresIn) {
        const tokenTimestamp = parsedTokenData.timestamp || Date.now(); // 如果没有时间戳，假设是当前时间
        const expiryTime = tokenTimestamp + (parsedTokenData.expiresIn * 1000);
        
        // 如果token已过期，清除并返回false
        if (Date.now() > expiryTime) {
          localStorage.removeItem('token');
          localStorage.removeItem('tokenData');
          localStorage.removeItem('userData');
          return false;
        }
      }
    } catch (e) {
      console.error('解析token数据时出错:', e);
    }
  }
  
  return true;
}

export function redirectToLogin() {
  // 保存当前路由路径
  const currentPath = router.currentRoute.value.fullPath;
  // 排除登录页本身和其他不需要重定向的页面
  if (!currentPath.startsWith('/login') && 
      !currentPath.startsWith('/register') && 
      !currentPath.startsWith('/forgot-password')) {
    localStorage.setItem('redirectFrom', currentPath);
  }
  
  // 防止重复提示
  const lastToastTime = sessionStorage.getItem('lastLoginToastTime');
  const now = Date.now();
  
  // 如果距离上次提示超过3秒，才显示提示
  if (!lastToastTime || (now - parseInt(lastToastTime)) > 3000) {
    // 弹出未登录提醒
    if (typeof window !== 'undefined' && window.$toast) {
      window.$toast.warning('请先登录');
    } else if (window && window.Toast && typeof window.Toast === 'function') {
      window.Toast('请先登录');
    }
    
    // 记录本次提示时间
    sessionStorage.setItem('lastLoginToastTime', now.toString());
  }
  
  // 跳转到登录页
  router.push('/login');
}