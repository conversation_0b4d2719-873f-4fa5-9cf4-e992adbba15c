/**
 * 设备检测工具
 * 提供设备类型、屏幕方向和尺寸检测功能
 */

/**
 * 获取当前设备信息
 * @returns {Object} 设备信息对象
 */
export function getDeviceInfo() {
  const width = window.innerWidth
  const height = window.innerHeight
  const userAgent = navigator.userAgent.toLowerCase()
  
  // 检测设备类型
  const isMobile = /iphone|ipod|android|blackberry|opera mini|opera mobi|skyfire|maemo|windows phone|palm|iemobile|symbian|symbianos|fennec/i.test(userAgent)
  const isTablet = /ipad|tablet|playbook|silk|android(?!.*mobile)/i.test(userAgent)
  const isDesktop = !isMobile && !isTablet
  
  // 检测操作系统
  const isIOS = /ipad|iphone|ipod/i.test(userAgent) && !window.MSStream
  const isAndroid = /android/i.test(userAgent)
  
  // 检测屏幕方向
  const isLandscape = width > height
  const isPortrait = !isLandscape
  
  // 检测是否支持触摸
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  
  // 检测是否为移动浏览器
  const isMobileBrowser = isMobile || (isTablet && isTouchDevice)
  
  // 检测是否为小屏幕设备
  const isSmallScreen = width < 768
  const isMediumScreen = width >= 768 && width < 1024
  const isLargeScreen = width >= 1024
  
  // 检测是否为低高度屏幕
  const isLowHeight = height < 600
  
  return {
    width,
    height,
    isMobile,
    isTablet,
    isDesktop,
    isIOS,
    isAndroid,
    isLandscape,
    isPortrait,
    isTouchDevice,
    isMobileBrowser,
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    isLowHeight,
    aspectRatio: width / height,
    pixelRatio: window.devicePixelRatio || 1
  }
}

/**
 * 创建设备变化观察器
 * @param {Function} callback 设备信息变化时的回调函数
 * @returns {Function} 清理函数，用于移除事件监听器
 */
export function createDeviceObserver(callback) {
  if (!callback || typeof callback !== 'function') {
    console.error('设备观察器需要一个回调函数')
    return () => {}
  }
  
  // 初始设备信息
  let currentInfo = getDeviceInfo()
  
  // 处理窗口大小变化
  const handleResize = () => {
    const newInfo = getDeviceInfo()
    
    // 检查是否有重要变化
    const hasImportantChanges = 
      newInfo.isLandscape !== currentInfo.isLandscape ||
      newInfo.width !== currentInfo.width ||
      newInfo.height !== currentInfo.height
    
    if (hasImportantChanges) {
      currentInfo = newInfo
      callback(newInfo)
    }
  }
  
  // 处理设备方向变化
  const handleOrientationChange = () => {
    // 方向变化后需要一点时间才能获取正确的窗口尺寸
    setTimeout(() => {
      const newInfo = getDeviceInfo()
      currentInfo = newInfo
      callback(newInfo)
    }, 100)
  }
  
  // 添加事件监听器
  window.addEventListener('resize', handleResize)
  window.addEventListener('orientationchange', handleOrientationChange)
  
  // 返回清理函数
  return () => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('orientationchange', handleOrientationChange)
  }
}

/**
 * 检测是否有安全区域
 * @returns {Boolean} 是否有安全区域
 */
export function hasSafeArea() {
  // 检测是否支持CSS环境变量
  const supportsCSS = window.CSS && window.CSS.supports && window.CSS.supports('(--foo: bar)')
  
  if (!supportsCSS) return false
  
  // 检测是否有安全区域
  const safeAreaBottom = getComputedStyle(document.documentElement).getPropertyValue('--safe-area-inset-bottom')
  
  return safeAreaBottom && safeAreaBottom !== '0px'
}

/**
 * 设置安全区域CSS变量
 */
export function setSafeAreaVariables() {
  if ('CSS' in window && CSS.supports('(--foo: bar)')) {
    const root = document.documentElement
    
    // 获取安全区域
    const safeAreaTop = getSafeAreaInset('top')
    const safeAreaRight = getSafeAreaInset('right')
    const safeAreaBottom = getSafeAreaInset('bottom')
    const safeAreaLeft = getSafeAreaInset('left')
    
    // 设置CSS变量
    root.style.setProperty('--safe-area-inset-top', `${safeAreaTop}px`)
    root.style.setProperty('--safe-area-inset-right', `${safeAreaRight}px`)
    root.style.setProperty('--safe-area-inset-bottom', `${safeAreaBottom}px`)
    root.style.setProperty('--safe-area-inset-left', `${safeAreaLeft}px`)
  }
}

/**
 * 获取安全区域插入值
 * @param {string} position 位置 (top, right, bottom, left)
 * @returns {number} 安全区域插入值（像素）
 */
function getSafeAreaInset(position) {
  // 尝试从CSS环境变量获取安全区域
  const safeArea = getComputedStyle(document.documentElement).getPropertyValue(
    `--safe-area-inset-${position}`
  )
  
  if (safeArea && safeArea !== '') {
    return parseInt(safeArea, 10)
  }
  
  // iOS设备上的安全区域
  if (window.webkit && window.webkit.messageHandlers) {
    // 尝试获取iOS安全区域
    try {
      const valueStr = window.getComputedStyle(document.documentElement)
        .getPropertyValue(`--safe-area-inset-${position}`)
        .trim()
      if (valueStr) {
        return parseInt(valueStr, 10) || 0
      }
    } catch (e) {
      console.error('Error getting safe area inset:', e)
    }
  }
  
  // 默认值
  if (position === 'bottom') {
    // 如果是iPhone X或更新的设备，底部安全区域默认为34px
    const isIPhoneWithNotch = /iPhone/.test(navigator.userAgent) && 
      (window.screen.height >= 812 || window.screen.width >= 812)
    return isIPhoneWithNotch ? 34 : 0
  }
  
  return 0
}

/**
 * 检测是否为PWA模式
 * @returns {Boolean} 是否为PWA模式
 */
export function isPWA() {
  return window.matchMedia('(display-mode: standalone)').matches || 
         window.navigator.standalone === true
}

/**
 * 检测是否为深色模式
 * @returns {Boolean} 是否为深色模式
 */
export function isDarkMode() {
  return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
}

/**
 * 监听深色模式变化
 * @param {Function} callback 深色模式变化时的回调函数
 * @returns {Function} 清理函数，用于移除事件监听器
 */
export function watchDarkMode(callback) {
  if (!callback || typeof callback !== 'function') {
    return () => {}
  }
  
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  
  const handleChange = (e) => {
    callback(e.matches)
  }
  
  // 添加事件监听器
  if (mediaQuery.addEventListener) {
    mediaQuery.addEventListener('change', handleChange)
  } else if (mediaQuery.addListener) {
    // 兼容旧版浏览器
    mediaQuery.addListener(handleChange)
  }
  
  // 返回清理函数
  return () => {
    if (mediaQuery.removeEventListener) {
      mediaQuery.removeEventListener('change', handleChange)
    } else if (mediaQuery.removeListener) {
      mediaQuery.removeListener(handleChange)
    }
  }
}