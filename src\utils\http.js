import axios from 'axios';
import router from '../router';

// 创建axios实例
const http = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
  timeout: 15000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 获取存储的token
    const token = localStorage.getItem('token');
    // 如果有token且不是登录请求，则添加到请求头
    // 使用正则表达式或更精确的URL路径匹配，避免字符串包含导致的安全问题
    if (token && !/\/auth\/oauth\/token(\?|$)/.test(config.url)) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    // 检查是否是token无效的响应
    const responseData = response.data;
    
    // 统一处理token失效的情况
    if (responseData && 
        ((responseData.code === -1 && responseData.msg === "token参数无效") || 
         (responseData.code === 401) || 
         (responseData.status === 401))) {
      console.log('Token无效或已过期，重定向到登录页面');
      // 清除本地存储的token
      localStorage.removeItem('token');
      localStorage.removeItem('tokenData');
      localStorage.removeItem('userData');
      // 重定向到登录页面
      router.replace('/login');
      return Promise.reject(new Error('登录已过期，请重新登录'));
    }
    
    // 如果响应成功，直接返回数据
    return responseData;
  },
  (error) => {
    // 处理错误响应
    if (error.response) {
      // 服务器返回了错误状态码
      switch (error.response.status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('token');
          localStorage.removeItem('tokenData');
          localStorage.removeItem('userData');
          router.replace('/login');
          return Promise.reject(new Error('登录已过期，请重新登录'));
        case 403:
          // 权限不足
          console.error('权限不足');
          return Promise.reject(new Error('您没有权限执行此操作'));
        case 404:
          // 资源不存在
          console.error('请求的资源不存在');
          return Promise.reject(new Error('请求的资源不存在'));
        case 500:
          // 服务器错误
          console.error('服务器错误');
          return Promise.reject(new Error('服务器错误，请稍后再试'));
        default:
          console.error(`未处理的错误状态码: ${error.response.status}`);
          return Promise.reject(new Error(`请求失败(${error.response.status})`));
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('网络错误，无法连接到服务器');
      return Promise.reject(new Error('网络错误，请检查您的网络连接'));
    } else {
      // 请求配置出错
      console.error('请求配置错误:', error.message);
      return Promise.reject(error);
    }
  }
);

// 封装GET请求
export const get = (url, config = {}) => {
  return http.get(url, config);
};

// 封装POST请求
export const post = (url, data = {}, options = {}) => {
  return http.post(url, data, options);
};

// 封装PUT请求
export const put = (url, data = {}) => {
  return http.put(url, data);
};

// 封装DELETE请求
export const del = (url, params = {}) => {
  return http.delete(url, { params });
};

// 通用请求方法
export const request = (method, url, data = {}, options = {}) => {
  return http({
    method,
    url,
    data: method.toLowerCase() !== 'get' ? data : null,
    params: method.toLowerCase() === 'get' ? data : null,
    ...options
  });
};

export default http;