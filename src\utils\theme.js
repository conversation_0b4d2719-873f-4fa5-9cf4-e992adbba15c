/**
 * 主题管理工具
 */

// 主题类型
export const THEME_TYPE = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system' // 跟随系统
};

// 本地存储的主题设置键名
const THEME_STORAGE_KEY = 'fin_mobile_theme';

/**
 * 获取当前系统的主题偏好
 * @returns {string} 'light' 或 'dark'
 */
export function getSystemTheme() {
  return window.matchMedia('(prefers-color-scheme: dark)').matches
    ? THEME_TYPE.DARK
    : THEME_TYPE.LIGHT;
}

/**
 * 获取当前主题设置
 * @returns {string} 'light', 'dark' 或 'system'
 */
export function getThemeSetting() {
  return localStorage.getItem(THEME_STORAGE_KEY) || THEME_TYPE.SYSTEM;
}

/**
 * 设置主题
 * @param {string} theme 'light', 'dark' 或 'system'
 */
export function setTheme(theme) {
  // 保存设置到本地存储
  localStorage.setItem(THEME_STORAGE_KEY, theme);
  
  // 应用主题
  applyTheme(theme);
}

/**
 * 应用主题到DOM
 * @param {string} theme 'light', 'dark' 或 'system'
 */
export function applyTheme(theme) {
  // 如果是跟随系统，则获取系统主题
  const actualTheme = theme === THEME_TYPE.SYSTEM 
    ? getSystemTheme() 
    : theme;
  
  // 应用深色模式类
  if (actualTheme === THEME_TYPE.DARK) {
    document.body.classList.add('dark-theme');
  } else {
    document.body.classList.remove('dark-theme');
  }
  
  // 更新meta主题色
  updateThemeColor(actualTheme);
}

/**
 * 更新meta主题色
 * @param {string} theme 'light' 或 'dark'
 */
function updateThemeColor(theme) {
  // 获取主题色meta标签
  let metaThemeColor = document.querySelector('meta[name="theme-color"]');
  
  // 如果不存在则创建
  if (!metaThemeColor) {
    metaThemeColor = document.createElement('meta');
    metaThemeColor.name = 'theme-color';
    document.head.appendChild(metaThemeColor);
  }
  
  // 设置主题色
  const color = theme === THEME_TYPE.DARK ? '#121212' : '#ffffff';
  metaThemeColor.content = color;
}

/**
 * 初始化主题
 * 在应用启动时调用
 */
export function initTheme() {
  // 获取保存的主题设置
  const savedTheme = getThemeSetting();
  
  // 应用主题
  applyTheme(savedTheme);
  
  // 监听系统主题变化
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
    // 如果设置为跟随系统，则在系统主题变化时更新
    if (getThemeSetting() === THEME_TYPE.SYSTEM) {
      applyTheme(THEME_TYPE.SYSTEM);
    }
  });
}