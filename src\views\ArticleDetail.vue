<template>
  <div class="article-detail-page">
    <div class="top-bar">
      <button class="top-bar-back" @click="goBack">←</button>
      <span class="top-bar-title">{{ article.title }}</span>
    </div>
    <div v-if="loading" class="loading">{{ $t('common.loading') }}</div>
    <div v-else-if="error" class="error">{{ error }}</div>
    <div v-else>
      <div class="main-card">
        <div v-if="article.image" class="cover"><img :src="article.image" alt="cover" /></div>
        <div class="title-big">{{ article.title }}</div>
        <div class="meta">
          <span class="author-badge">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#1bc47d" stroke-width="2"><circle cx="12" cy="8" r="4"/><path d="M6 20v-2a4 4 0 0 1 8 0v2"/></svg>
            {{ article.author }}
          </span>
          <span class="meta-item">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#888" stroke-width="2"><path d="M3 12a9 9 0 1 1 18 0 9 9 0 0 1-18 0zm9-4v4l3 3"/></svg>
            {{ article.create_time }}
          </span>
          <span class="meta-item">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#888" stroke-width="2"><path d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/><path d="M2.05 13A10 10 0 1 1 21.95 13"/></svg>
            {{ article.click }}
          </span>
        </div>
        <div class="abstract" v-if="article.abstract || article.desc">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#1bc47d" stroke-width="2"><rect x="3" y="7" width="18" height="13" rx="4"/><path d="M16 3v4"/><path d="M8 3v4"/></svg>
          {{ article.abstract || article.desc }}
        </div>
        <div class="article-content" v-html="article.content"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import articleApi from '@/api/article'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const id = route.query.id
const article = ref({})
const loading = ref(true)
const error = ref(null)

const goBack = () => router.back()

const fetchArticle = async () => {
  loading.value = true
  error.value = null
  try {
    const res = await articleApi.getArticleDetail(id)
    article.value = res.data || {}
  } catch (e) {
    error.value = t('common.loading') + ' failed'
  } finally {
    loading.value = false
  }
}

onMounted(fetchArticle)
</script>

<style scoped lang="scss">
.article-detail-page {
  min-height: 100vh;
  background: #f5f6fa;
  padding-bottom: 32px;
}
.top-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid #f0f0f0;
  max-width: 480px;
  margin: 0 auto;
}
.top-bar-back {
  position: absolute;
  left: 16px;
  background: none;
  border: none;
  font-size: 22px;
  color: #222;
  cursor: pointer;
  padding: 0;
  height: 56px;
  display: flex;
  align-items: center;
}
.top-bar-title {
  font-size: 20px;
  font-weight: 800;
  color: #111;
  text-align: center;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.loading, .error {
  text-align: center;
  color: #888;
  margin-top: 32px;
}
.main-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.07);
  max-width: 480px;
  margin: 0 auto;
  padding: 0 0 24px 0;
  position: relative;
  overflow: hidden;
}
.cover {
  width: 100%;
  text-align: center;
  margin-bottom: 0;
  background: #f8f8f8;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}
.cover img {
  width: 100%;
  max-height: 220px;
  object-fit: cover;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.08);
}
.title-big {
  font-size: 26px;
  font-weight: 900;
  color: #222;
  margin: 18px 24px 8px 24px;
  line-height: 1.3;
  text-align: left;
}
.meta {
  display: flex;
  flex-wrap: wrap;
  gap: 14px;
  font-size: 13px;
  color: #888;
  margin: 0 24px 8px 24px;
  align-items: center;
}
.author-badge {
  background: #e8f8f2;
  color: #1bc47d;
  border-radius: 12px;
  padding: 2px 10px 2px 6px;
  display: flex;
  align-items: center;
  font-weight: 600;
  gap: 4px;
}
.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}
.abstract {
  margin: 0 24px 18px 24px;
  font-size: 15px;
  color: #555;
  background: #f8f8f8;
  border-radius: 6px;
  padding: 10px 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}
.article-content {
  margin: 0 24px;
  font-size: 16px;
  color: #222;
  line-height: 1.7;
  word-break: break-word;
  padding-top: 8px;
}
@media (max-width: 600px) {
  .main-card, .header {
    max-width: 100vw;
    border-radius: 0;
  }
  .main-card {
    margin: 0;
  }
  .cover img {
    border-radius: 0;
  }
  .title-big, .meta, .abstract, .article-content {
    margin-left: 12px;
    margin-right: 12px;
  }
}
</style> 