<template>
  <div class="article-list-page">
    <div class="top-bar">
      <button class="top-bar-back" @click="goBack">←</button>
      <span class="top-bar-title">{{ title }}</span>
    </div>
    <div v-if="loading" class="loading">{{ $t('common.loading') }}</div>
    <div v-else-if="error" class="error">{{ error }}</div>
    <div v-else>
      <div v-if="!articles.lists || articles.lists.length === 0" class="empty">{{ $t('common.noActivity') }}</div>
      <ul class="article-list">
        <li v-for="(item, idx) in articles.lists" :key="item.id" class="article-item" @click="goDetail(item)">
          <span class="article-index">{{ idx + 1 }}</span>
          <div class="article-title">{{ item.title }}</div>
          <div class="article-summary" v-if="item.summary">{{ item.summary }}</div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import articleApi from '@/api/article'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const cid = route.query.cid
const title = route.query.title || t('help.title')
const articles = ref([])
const loading = ref(true)
const error = ref(null)

const goBack = () => router.back()

const goDetail = (item) => {
  router.push({ path: '/article-detail', query: { id: item.id, title: item.title } })
}

const fetchArticles = async () => {
  loading.value = true
  error.value = null
  try {
    const res = await articleApi.getArticleListByCategory(cid)
    // 假设返回数据结构为 { data: [{ id, title, summary, ... }] }
    articles.value = res.data || []
  } catch (e) {
    error.value = t('common.loading') + ' failed'
  } finally {
    loading.value = false
  }
}

onMounted(fetchArticles)
</script>

<style scoped lang="scss">
.article-list-page {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 32px;
}
.top-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid #f0f0f0;
  max-width: 480px;
  margin: 0 auto;
}
.top-bar-back {
  position: absolute;
  left: 16px;
  background: none;
  border: none;
  font-size: 22px;
  color: #222;
  cursor: pointer;
  padding: 0;
  height: 56px;
  display: flex;
  align-items: center;
}
.top-bar-title {
  font-size: 20px;
  font-weight: 800;
  color: #111;
  text-align: center;
  flex: 1;
}
.loading, .error, .empty {
  text-align: center;
  color: #888;
  margin-top: 32px;
}
.article-list {
  max-width: 420px;
  margin: 0 auto;
  padding: 0 16px;
  list-style: none;
}
.article-item {
  padding: 16px 0;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
}
.article-title {
  font-size: 16px;
  font-weight: 600;
  color: #222;
  flex: 1;
}
.article-summary {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}
.article-index {
  width: 28px;
  height: 28px;
  background: #f3f5f7;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  color: #888;
  font-weight: 600;
  margin-right: 12px;
}
</style> 