<template>
  <div class="asset-page">
    <!-- 顶部黑色大背景 -->
    <div class="asset-header-bg"></div>
    <!-- 顶部栏 -->
    <div class="asset-topbar">
      <span class="asset-title">{{ $t('asset.title') }}</span>
      <button class="asset-topbar-icon" aria-label="info">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <circle cx="12" cy="12" r="2"/>
        </svg>
      </button>
    </div>
    <!-- 资产卡片浮动装饰 -->
    <div class="asset-card-side-circle left"></div>
    <div class="asset-card-side-circle right"></div>
    <!-- 账户卡片 -->
    <div class="asset-card card-margin-top">
      <div class="asset-overview">
        <div class="asset-card-row">
          <img class="asset-avatar" src="https://randomuser.me/api/portraits/men/32.jpg" alt="avatar" />
          <div class="asset-card-info">
            <div class="asset-card-title">{{ $t('asset.info.availableAsset') }}</div>
            <div class="asset-card-balance" :class="{ 'blur-text': hideBalance }">{{ hideBalance ? '******' : '$149,478.25' }}</div>
            <div class="asset-card-cny" :class="{ 'blur-text': hideBalance }">{{ hideBalance ? '******' : '≈￥918,689.75' }}</div>
          </div>
          <button class="asset-eye-btn" @click="toggleBalanceVisibility">
            <svg width="22" height="22" viewBox="0 0 24 24" fill="none" :stroke="hideBalance ? '#888' : '#222'" stroke-width="2">
              <template v-if="!hideBalance">
                <ellipse cx="12" cy="12" rx="8" ry="5"/>
                <circle cx="12" cy="12" r="2.5"/>
              </template>
              <template v-else>
                <ellipse cx="12" cy="12" rx="8" ry="5"/>
                <line x1="3" y1="3" x2="21" y2="21" />
              </template>
            </svg>
          </button>
        </div>
      </div>
      <div class="asset-card-actions">
        <button class="asset-action asset-action-main" @click="handleAction('deposit')">{{ $t('asset.actions.deposit') }}</button>
        <button class="asset-action" @click="handleAction('withdraw')">{{ $t('asset.actions.withdraw') }}</button>
        <!-- <button class="asset-action" @click="handleAction('exchange')">兑换</button> -->
      </div>
    </div>
    <!-- 资产列表 -->
    <div class="asset-list-container">
      <div class="asset-list-header">
        <span class="asset-list-title">{{ $t('asset.info.availableAsset') }}</span>
        <div class="asset-list-actions">
          <button class="asset-list-action">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#666" stroke-width="2"><line x1="8" y1="6" x2="21" y2="6"></line><line x1="8" y1="12" x2="21" y2="12"></line><line x1="8" y1="18" x2="21" y2="18"></line><line x1="3" y1="6" x2="3.01" y2="6"></line><line x1="3" y1="12" x2="3.01" y2="12"></line><line x1="3" y1="18" x2="3.01" y2="18"></line></svg>
          </button>
          <div class="asset-search-container">
            <input 
              type="text" 
              class="asset-search-input" 
              :placeholder="$t('asset.searchPlaceholder')" 
              v-model="searchQuery"
              @focus="isSearchFocused = true"
              @blur="isSearchFocused = false"
            />
            <svg 
              class="asset-search-icon" 
              width="18" 
              height="18" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="#666" 
              stroke-width="2"
            >
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </div>
        </div>
      </div>
      
      <div class="asset-list-container">
        <div class="asset-table-header">
          <div class="asset-table-col asset-col-name">
            {{ $t('asset.detail.coin') }}
            <span class="sort-arrow" @click="sortBy('symbol')">▼</span>
          </div>
          <div class="asset-table-col asset-col-trend">
            {{ $t('asset.detail.type') }}
          </div>
          <div class="asset-table-col asset-col-balance">
            {{ $t('asset.detail.amount') }}
            <span class="sort-arrow" @click="sortBy('balance')">▼</span>
          </div>
        </div>
        <div class="asset-list">
          <div 
            class="asset-item" 
            v-for="(asset, index) in filteredAssetList" 
            :key="index"
            @click="navigateToDetail(asset)"
          >
            <div class="asset-item-icon">
              <img :src="asset.icon" :alt="asset.name" />
            </div>
            <div class="asset-item-info">
              <div class="asset-item-symbol">{{ asset.symbol }}</div>
              <div class="asset-item-name">{{ asset.name }}</div>
            </div>
            <div class="asset-item-trend">
              <svg class="asset-trend-chart" viewBox="0 0 100 30">
                <path 
                  :d="asset.trendPath || 'M0,15 Q25,5 50,15 T100,15'" 
                  :stroke="asset.change >= 0 ? '#4CAF50' : '#FF5252'" 
                  stroke-width="2" 
                  fill="none"
                />
              </svg>
            </div>
            <div class="asset-item-balance">
              <div class="asset-item-amount" :class="{ 'blur-text': hideBalance }">
                {{ hideBalance ? '******' : formatNumber(asset.balance) }}
              </div>
              <div class="asset-item-fiat" :class="{ 'blur-text': hideBalance }">
                {{ hideBalance ? '******' : `$${asset.fiat}` }}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 骨架屏 -->
      <div class="asset-skeleton" v-if="isLoading">
        <div class="asset-skeleton-item" v-for="i in 5" :key="i">
          <div class="asset-skeleton-icon"></div>
          <div class="asset-skeleton-info">
            <div class="asset-skeleton-name"></div>
            <div class="asset-skeleton-amount"></div>
          </div>
          <div class="asset-skeleton-trend"></div>
          <div class="asset-skeleton-value">
            <div class="asset-skeleton-price"></div>
            <div class="asset-skeleton-total"></div>
          </div>
        </div>
      </div>
    </div>
    <TabBar />
  </div>
</template>

<script setup>
import TabBar from '../components/TabBar.vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ref, computed, onMounted } from 'vue'
import { redirectToLogin } from '@/utils/auth'

const { t } = useI18n()
const router = useRouter()
const hideBalance = ref(false)
const isLoading = ref(true)
const searchQuery = ref('')
const activeTab = ref('all')
const showDistribution = ref(false)
const isSearchFocused = ref(false)

// 增强的资产数据
const assetList = [
  {
    symbol: 'BTC',
    name: 'Bitcoin',
    icon: new URL('../assets/btc.svg', import.meta.url).href,
    trendSvg: `<svg width='64' height='24' viewBox='0 0 64 24'><polyline points='0,18 8,10 16,12 24,8 32,14 40,6 48,10 56,4 64,8' fill='none' stroke='#1bc47d' stroke-width='2' stroke-linecap='round'/></svg>`,
    balance: 3.46586,
    fiat: '61,667.13',
    trend: 2, // mock 趋势分数
    change: 2.45, // 24h 变化百分比
    percentage: 85, // 资产占比
    color: '#F7931A', // 品牌颜色
  },
  {
    symbol: 'ETH',
    name: 'Ethereum',
    icon: new URL('../assets/eth.svg', import.meta.url).href,
    trendSvg: `<svg width='64' height='24' viewBox='0 0 64 24'><polyline points='0,8 8,12 16,10 24,16 32,12 40,18 48,14 56,20 64,16' fill='none' stroke='#f44336' stroke-width='2' stroke-linecap='round'/></svg>`,
    balance: 6.225,
    fiat: '8,232.19',
    trend: 1, // mock 趋势分数
    change: -1.32, // 24h 变化百分比
    percentage: 8, // 资产占比
    color: '#627EEA', // 品牌颜色
  },
  {
    symbol: 'SOL',
    name: 'Solana',
    icon: new URL('../assets/sol.svg', import.meta.url).href,
    trendSvg: `<svg width='64' height='24' viewBox='0 0 64 24'><polyline points='0,18 8,10 16,12 24,8 32,14 40,6 48,10 56,4 64,8' fill='none' stroke='#1bc47d' stroke-width='2' stroke-linecap='round'/></svg>`,
    balance: 126.84,
    fiat: '1,763.71',
    trend: 3, // mock 趋势分数
    change: 5.67, // 24h 变化百分比
    percentage: 4, // 资产占比
    color: '#00FFA3', // 品牌颜色
  },
  {
    symbol: 'USDT',
    name: 'Tether',
    icon: new URL('../assets/usdt.svg', import.meta.url).href,
    trendSvg: `<svg width='64' height='24' viewBox='0 0 64 24'><polyline points='0,18 8,10 16,12 24,8 32,14 40,6 48,10 56,4 64,8' fill='none' stroke='#1bc47d' stroke-width='2' stroke-linecap='round'/></svg>`,
    balance: 4936.25,
    fiat: '4,936.05',
    trend: 4, // mock 趋势分数
    change: 0.01, // 24h 变化百分比
    percentage: 3, // 资产占比
    color: '#26A17B', // 品牌颜色
  },
]

// 排序功能
const sortKey = ref('balance')
const sortOrder = ref('desc')

function sortBy(key) {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortKey.value = key
    sortOrder.value = 'desc'
  }
}

// 搜索和过滤功能
const filteredAssetList = computed(() => {
  let result = [...assetList]
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(asset => 
      asset.name.toLowerCase().includes(query) || 
      asset.symbol.toLowerCase().includes(query)
    )
  }
  
  // 标签过滤
  if (activeTab.value === 'favorites') {
    // 这里可以添加收藏功能的逻辑
    result = result.filter(asset => ['BTC', 'ETH'].includes(asset.symbol))
  }
  
  // 排序
  if (sortKey.value === 'symbol') {
    result.sort((a, b) => sortOrder.value === 'asc' ? a.symbol.localeCompare(b.symbol) : b.symbol.localeCompare(a.symbol))
  } else if (sortKey.value === 'trend') {
    result.sort((a, b) => sortOrder.value === 'asc' ? a.trend - b.trend : b.trend - a.trend)
  } else if (sortKey.value === 'balance') {
    result.sort((a, b) => sortOrder.value === 'asc' ? a.balance - b.balance : b.balance - a.balance)
  }
  
  return result
})

// 格式化数字，添加千位分隔符
function formatNumber(num) {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(num)
}

// 获取币种颜色
function getCoinColor(symbol) {
  const coin = assetList.find(c => c.symbol === symbol)
  return coin ? coin.color : '#888888'
}

// 获取币种浅色背景
function getCoinColorLight(symbol) {
  const coin = assetList.find(c => c.symbol === symbol)
  return coin ? `${coin.color}15` : '#f5f5f5' // 15是透明度
}

// 获取饼图偏移角度
function getOffset(index) {
  let offset = 0
  for (let i = 0; i < index; i++) {
    offset += filteredAssetList.value[i].percentage * 3.6 // 360度 / 100 = 3.6
  }
  return offset
}

// 切换余额显示/隐藏
function toggleBalanceVisibility() {
  hideBalance.value = !hideBalance.value
}

// 导航到详情页
function navigateToDetail(asset) {
  router.push(`/coin/${asset.symbol.toLowerCase()}`)
}

// 处理操作按钮点击
function handleAction(type) {
  if (type === 'deposit') router.push('/payment')
  if (type === 'withdraw') router.push('/withdraw')
  if (type === 'exchange') router.push('/exchange')
}

// 检查登录状态
function isLoggedIn() {
  // 1. 首先检查token是否存在
  const token = localStorage.getItem('token')
  if (token) {
    return true // token存在即认为已登录
  }
  
  // 2. 没有token时检查用户信息
  const userStr = localStorage.getItem('user_cache')
  return !!userStr
}

// 页面加载
onMounted(() => {
  if (!isLoggedIn()) {
    redirectToLogin()
    return
  }
  
  // 模拟加载
  setTimeout(() => {
    isLoading.value = false
  }, 500)
})
</script>

<style scoped lang="scss">
@use 'sass:color';
// 基础变量
$primary: #1bc47d;
$primary-light: #e6f8f1;
$primary-dark: #0a9e5c;
$danger: #f44336;
$danger-light: #ffebee;
$dark: #111;
$text-primary: #222;
$text-secondary: #888;
$background-light: #f7f8fa;
$card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$radius-sm: 6px;
$radius-md: 10px;
$radius-lg: 14px;

// 页面基础样式
.asset-page {
  max-width: 420px;
  margin: 0 auto;
  width: 100%;
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
  padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px));
  position: relative;
  overflow: hidden;
}

// 顶部黑色大背景
.asset-header-bg {
  position: absolute;
  left: 0; top: 0; right: 0;
  height: 160px;
  background: #000;
  z-index: 0;
}
// 顶部白色“资产”标题
.asset-header-title {
  position: absolute;
  left: 20px;
  top: 28px;
  color: #fff;
  font-size: 22px;
  font-weight: 800;
  letter-spacing: 1px;
  z-index: 2;
}
// 顶部栏 - 添加渐变背景
.asset-topbar {
  background: transparent;
  color: #fff;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 28px;
  font-size: 20px;
  font-weight: 700;
  box-shadow: none;
  position: relative;
  z-index: 2;
}

.asset-title {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 1px;
}

.asset-topbar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #111;
  cursor: pointer;
  transition: background 0.2s;
  border: 1.5px solid #fff;
  box-sizing: border-box;
  margin-left: 8px;
  svg {
    display: block;
  }
  &:hover {
    background: #222;
  }
}
// 资产卡片浮动装饰
.asset-card-side-circle {
  position: absolute;
  top: 120px;
  width: 48px;
  height: 48px;
  background: #000;
  border-radius: 50%;
  z-index: 1;
}
.asset-card-side-circle.left {
  left: -24px;
}
.asset-card-side-circle.right {
  right: -24px;
}
// 账户卡片 - 优化设计
.asset-card {
  background: #fff;
  border-radius: 18px; // 更大圆角
  box-shadow: 0 6px 24px rgba(0,0,0,0.10); // 更柔和阴影
  margin: 0 12px 18px 12px; // 上下margin更大
  margin-top: 40px !important;
  padding: 24px 20px 20px 20px; // padding更大
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: column;
  gap: 18px;
  transition: transform $transition-normal, box-shadow $transition-normal;
  animation: fadeIn 0.5s cubic-bezier(.4,0,.2,1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  }
}

.card-margin-top {
  margin-top: 20px !important;
  margin-bottom: 16px !important;
}

.asset-overview {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 8px;
}

.asset-card-title-main {
  font-size: 20px;
  font-weight: 800;
  color: #222;
  margin-bottom: 12px;
  margin-left: 2px;
  letter-spacing: 1px;
}

.asset-card-row {
  display: flex;
  align-items: center;
  gap: 16px;
}

.asset-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  object-fit: cover;
  background: #eee;
  box-shadow: 0 2px 8px rgba(0,0,0,0.12);
  border: 2.5px solid #fff;
}

.asset-card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.asset-card-title {
  font-size: 15px;
  color: #222;
  font-weight: 700;
  margin-bottom: 2px;
}

.asset-card-balance {
  font-size: 32px;
  font-weight: 800;
  color: #111;
  margin: 0 0 2px 0;
  letter-spacing: 0.5px;
}

.asset-card-cny {
  font-size: 13px;
  color: #888;
  margin-top: 0;
}

.blur-text {
  filter: blur(6px);
}

.asset-eye-btn {
  background: none;
  border: none;
  padding: 6px;
  margin-left: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-radius: 50%;
  transition: background-color 0.18s;
  svg {
    width: 22px;
    height: 22px;
  }
  &:hover {
    background-color: #f5f5f5;
  }
  &:active {
    background-color: #eee;
  }
}

// 资产统计
.asset-stats {
  display: flex;
  gap: 16px;
  margin-top: 4px;
}

.asset-stat-item {
  flex: 1;
  background: $background-light;
  border-radius: $radius-md;
  padding: 10px 14px;
  transition: transform $transition-fast;
  
  &:hover {
    transform: translateY(-2px);
  }
}

.asset-stat-label {
  font-size: 12px;
  color: $text-secondary;
  margin-bottom: 4px;
}

.asset-stat-value {
  font-size: 16px;
  font-weight: 700;
  
  &.asset-up {
    color: $primary;
  }
  
  &.asset-down {
    color: $danger;
  }
}

// 操作按钮
.asset-card-actions {
  display: flex;
  gap: 12px;
  margin-top: 10px;
}

.asset-action {
  flex: 1;
  height: 44px;
  border-radius: 10px;
  border: none;
  font-size: 16px;
  font-weight: 700;
  background: #f7f8fa;
  color: #111;
  cursor: pointer;
  transition: all 0.18s;
  box-shadow: none;
  &:hover {
    background: #f0f0f0;
    color: #111;
  }
  &:active {
    background: #eaeaea;
  }
}

.asset-action-main {
  background: #111;
  color: #fff;
  &:hover {
    background: #222;
    color: #fff;
  }
  &:active {
    background: #000;
    color: #fff;
  }
}
// 资产列表容器
.asset-list-container {
  padding: 0 12px;
}

// 资产列表头部
.asset-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  padding: 0 2px;
}
.asset-list-title {
  font-size: 15px;
  font-weight: 700;
  color: #222;
  letter-spacing: 0.2px;
}
.asset-list-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}
.asset-list-action {
  background: #fff;
  border: 1.5px solid #eaeaea;
  padding: 6px;
  border-radius: 10px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.18s, border 0.18s;
  svg {
    width: 18px;
    height: 18px;
  }
  &:hover {
    background: #f5f5f5;
    border-color: #d0d0d0;
  }
}
.asset-search-container {
  position: relative;
  width: 260px;
  margin-left: 6px;
}
.asset-search-input {
  width: 100%;
  height: 36px;
  border-radius: 12px;
  border: 1.5px solid #eaeaea;
  background: #fafbfc;
  padding: 0 36px 0 32px;
  font-size: 15px;
  color: #222;
  outline: none;
  transition: border 0.18s, box-shadow 0.18s;
  box-shadow: none;
  &::placeholder {
    color: #bbb;
    font-size: 15px;
    font-weight: 400;
  }
  &:focus {
    border-color: #1bc47d;
    background: #fff;
    box-shadow: 0 0 0 2px rgba(27,196,125,0.08);
  }
}
.asset-search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  color: #bbb;
  pointer-events: none;
}
// 资产分布
.asset-distribution {
  background: #fff;
  border-radius: $radius-lg;
  box-shadow: $card-shadow;
  margin-bottom: 16px;
  padding: 16px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.asset-distribution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }
}

.asset-distribution-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: $text-secondary;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.asset-distribution-chart {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

// 饼图占位符
.asset-pie-placeholder {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: #f5f5f5;
  overflow: hidden;
}

.pie-segment {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: conic-gradient(
    var(--segment-color) 0deg,
    var(--segment-color) calc(var(--segment-size) * 3.6deg),
    transparent calc(var(--segment-size) * 3.6deg)
  );
  transform: rotate(var(--segment-offset));
}

.pie-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: #fff;
  border-radius: 50%;
}

.asset-distribution-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-name {
  font-size: 14px;
  color: $text-primary;
  flex: 1;
}

.legend-value {
  font-size: 14px;
  font-weight: 600;
  color: $text-primary;
}

// 资产列表标签
.asset-list-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
  align-items: center;
}

.asset-list-tab {
  padding: 8px 16px;
  border: none;
  background: $background-light;
  border-radius: $radius-md;
  font-size: 14px;
  font-weight: 600;
  color: $text-secondary;
  cursor: pointer;
  transition: all $transition-fast;
  
  &:hover {
    background: color.adjust($background-light, $lightness: -3%);
  }
  
  &.active {
    background: $primary;
    color: #fff;
  }
}

.asset-distribution-btn {
  margin-left: auto;
  padding: 8px 12px;
  border: none;
  background: $background-light;
  border-radius: $radius-md;
  font-size: 14px;
  font-weight: 600;
  color: $text-secondary;
  cursor: pointer;
  transition: all $transition-fast;
  display: flex;
  align-items: center;
  gap: 6px;
  
  &:hover {
    background: color.adjust($background-light, $lightness: -3%);
    color: $primary;
  }
}
// 资产列表
.asset-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.asset-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #fff;
  border-radius: $radius-lg;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  gap: 16px;
  cursor: pointer;
  transition: all $transition-fast;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

.asset-item-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 32px;
    height: 32px;
  }
}

.asset-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.asset-item-name {
  font-size: 16px;
  font-weight: 600;
  color: $text-primary;
}

.asset-item-amount {
  font-size: 14px;
  color: $text-secondary;
}

.asset-item-trend {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  width: 80px;
}

.asset-trend-percentage {
  font-size: 14px;
  font-weight: 600;
  
  &.asset-up {
    color: $primary;
  }
  
  &.asset-down {
    color: $danger;
  }
}

.asset-trend-chart {
  width: 80px;
  height: 30px;
}

.asset-item-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  min-width: 80px;
}

.asset-item-price {
  font-size: 14px;
  color: $text-secondary;
}

.asset-item-total {
  font-size: 16px;
  font-weight: 600;
  color: $text-primary;
}

// 骨架屏
.asset-skeleton {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.asset-skeleton-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #fff;
  border-radius: $radius-lg;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  gap: 16px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 0.3; box-shadow: 0 4px 16px rgba(0,0,0,0.10); }
  100% { opacity: 0.6; }
}

.asset-skeleton-icon,
.asset-skeleton-name,
.asset-skeleton-amount,
.asset-skeleton-trend,
.asset-skeleton-price,
.asset-skeleton-total {
  background: #f0f0f0;
  animation: pulse 1.5s infinite;
  border-radius: 4px;
}

.asset-skeleton-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.asset-skeleton-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.asset-skeleton-name {
  height: 16px;
  width: 80px;
}

.asset-skeleton-amount {
  height: 14px;
  width: 60px;
}

.asset-skeleton-trend {
  width: 80px;
  height: 30px;
}

.asset-skeleton-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.asset-skeleton-price {
  height: 14px;
  width: 60px;
}

.asset-skeleton-total {
  height: 16px;
  width: 70px;
}
// 响应式设计
@media (max-width: 768px) {
  .asset-distribution-chart {
    flex-direction: column;
  }
  
  .asset-item {
    padding: 12px;
    gap: 12px;
  }
  
  .asset-item-icon {
    width: 40px;
    height: 40px;
    
    img {
      width: 28px;
      height: 28px;
    }
  }
  
  .asset-item-trend {
    width: 60px;
  }
  
  .asset-trend-chart {
    width: 60px;
  }
  
  .asset-search-container {
    width: 180px;
  }
  .asset-search-input:focus {
    width: 180px;
  }
}

@media (max-width: 480px) {
  .asset-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .asset-list-tabs {
    overflow-x: auto;
    padding-bottom: 4px;
    
    &::-webkit-scrollbar {
      height: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }
  }
  
  .asset-item-trend {
    display: none;
  }
}

.asset-table-header {
  display: flex;
  align-items: center;
  padding: 0 8px 8px 8px;
  font-size: 14px;
  color: #888;
  font-weight: 600;
  background: #fff;
}

.asset-table-col {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 4px;
}

.asset-col-name { flex: 2; }
.asset-col-trend { flex: 2; justify-content: center; }
.asset-col-balance { flex: 2; justify-content: flex-end; }

.sort-arrow {
  font-size: 12px;
  color: #bbb;
  cursor: pointer;
}

.asset-item {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  background: #fff;
  border-radius: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all $transition-fast;
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

.asset-item-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  img {
    width: 28px;
    height: 28px;
  }
}

.asset-item-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .asset-item-symbol {
    font-size: 16px;
    font-weight: 700;
    color: #222;
  }
  .asset-item-name {
    font-size: 13px;
    color: #888;
    margin-top: 2px;
  }
}

.asset-item-trend {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.asset-trend-chart {
  width: 80px;
  height: 30px;
}

.asset-item-balance {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  .asset-item-amount {
    font-size: 16px;
    font-weight: 700;
    color: #222;
  }
  .asset-item-fiat {
    font-size: 13px;
    color: #888;
    margin-top: 2px;
  }
}
</style>