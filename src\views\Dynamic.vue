<template>
  <div class="dynamic-page">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <svg width="20" height="20" viewBox="0 0 24 24">
        <circle cx="11" cy="11" r="7" stroke="#bbb" stroke-width="2" fill="none"/>
        <line x1="18" y1="18" x2="22" y2="22" stroke="#bbb" stroke-width="2"/>
      </svg>
      <span class="hot-icon">🔥</span>
      <input type="text" :placeholder="$t('dynamic.searchPlaceholder')" />
    </div>
    
    <!-- 主Tab -->
    <div class="main-tabs">
      <span @click="$router.push('/market')">{{ $t('dynamic.tabs.market') }}</span>
      <span class="active">{{ $t('dynamic.tabs.dynamic') }}</span>
      <span @click="$router.push('/rank')">{{ $t('dynamic.tabs.rank') }}</span>
    </div>
    
    <!-- 二级tab -->
    <div class="dynamic-sub-tabs">
      <span 
        :class="{active: dynamicTab==='recommend'}" 
        @click="dynamicTab='recommend'"
      >
        {{ $t('dynamic.subTabs.recommend') }}
      </span>
      <span 
        :class="{active: dynamicTab==='newsflash'}" 
        @click="dynamicTab='newsflash'"
      >
        {{ $t('dynamic.subTabs.newsflash') }}
      </span>
    </div>
    
    <!-- 热点速递 -->
    <div class="hot-news-area" v-if="dynamicTab === 'recommend'">
      <div class="hot-title">{{ $t('dynamic.hotNews') }}</div>
      <div class="hot-list">
        <div v-for="(item, index) in hotList" :key="index" class="hot-item">
          <span class="hot-hash">#</span>{{ item }}
        </div>
      </div>
    </div>
    
    <!-- 动态流 -->
    <div class="dynamic-feed-list">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>{{ $t('common.loading') }}</p>
      </div>
      
      <div v-else-if="feedList.length === 0" class="empty-state">
        <div class="empty-icon">📝</div>
        <p>{{ $t('dynamic.noFeeds') }}</p>
      </div>
      
      <div v-else class="feed-card" v-for="feed in feedList" :key="feed.id">
        <img :src="feed.avatar" class="feed-avatar" @error="handleImageError" />
        <div class="feed-main">
          <div class="feed-header">
            <span class="feed-nick">{{ feed.nickname }}</span>
            <span class="feed-time">{{ feed.time }}</span>
          </div>
          <div class="feed-content">{{ feed.text }}</div>
          <div class="feed-links" v-if="feed.links && feed.links.length">
            <span v-for="(link, index) in feed.links" :key="index" class="feed-link">
              {{ link }}
            </span>
          </div>
          <img 
            v-if="feed.image" 
            :src="feed.image" 
            class="feed-img" 
            @error="handleImageError"
            @load="handleImageLoad"
          />
        </div>
      </div>
    </div>
    
    <TabBar />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import TabBar from '../components/TabBar.vue'

const { t } = useI18n()
const dynamicTab = ref('recommend')
const loading = ref(true)

// 热点话题列表
const hotList = ref([
  "Circle's total market cap surpasses USDC",
  "Trader Eugene: BTC has held $100,000, market panic may have peaked",
  "Female Buffett CRCL made 100M then sold, now shorting Circle?"
])

// 动态流数据
const feedList = ref([
  {
    id: 1,
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
    nickname: 'Ni',
    time: t('common.time.hoursAgo', { hours: 3 }),
    text: '并不是所有关于@satlayer 的事情都那么喧闹。\n真正的优势？在大家还在讨论的时候，悄悄地使用技术。',
    links: ['Connect Wallet', 'Connect Social', 'Click Discord', 'More'],
    image: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80'
  },
  {
    id: 2,
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
    nickname: 'CryptoTrader',
    time: t('common.time.hoursAgo', { hours: 1 }),
    text: 'Market analysis: Bitcoin showing strong support at current levels. Key resistance at $45,000.',
    links: ['View Chart', 'Follow'],
    image: null
  },
  {
    id: 3,
    avatar: 'https://randomuser.me/api/portraits/women/3.jpg',
    nickname: 'DeFi_Expert',
    time: t('common.time.minutesAgo', { minutes: 30 }),
    text: 'New DeFi protocol launched with innovative yield farming strategies. Early adopters seeing 200%+ APY.',
    links: ['Learn More', 'Join Community'],
    image: 'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?auto=format&fit=crop&w=400&q=80'
  }
])

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.style.display = 'none'
}

// 处理图片加载成功
const handleImageLoad = (event) => {
  event.target.style.opacity = '1'
}

// 模拟加载数据
onMounted(() => {
  setTimeout(() => {
    loading.value = false
  }, 1000)
})
</script>

<style scoped lang="scss">
.dynamic-page {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
  padding-bottom: 60px;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f6f6f6;
  border-radius: 18px;
  margin: 16px 16px 0 16px;
  padding: 8px 12px;
  
  svg { 
    margin-right: 6px; 
  }
  
  .hot-icon { 
    margin-right: 4px; 
  }
  
  input {
    border: none;
    background: transparent;
    font-size: 15px;
    flex: 1;
    outline: none;
    
    &::placeholder {
      color: #999;
    }
  }
}

.main-tabs {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 12px 0 0 0;
  border-bottom: 1.5px solid #eee;
  
  span {
    font-size: 16px;
    color: #888;
    padding: 10px 0;
    margin: 0 12px;
    cursor: pointer;
    position: relative;
    transition: color 0.3s ease;
    
    &:hover {
      color: #666;
    }
    
    &.active {
      color: #111;
      font-weight: bold;
      
      &::after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: -2px;
        transform: translateX(-50%);
        width: 32px;
        height: 3px;
        background: #111;
        border-radius: 2px;
      }
    }
  }
}

.dynamic-sub-tabs {
  display: flex;
  gap: 16px;
  margin: 18px 0 0 16px;
  font-size: 16px;
  
  span {
    color: #888;
    background: #f6f6f6;
    padding: 6px 22px;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: #e0e0e0;
    }
    
    &.active {
      background: #111;
      color: #fff;
      font-weight: bold;
    }
  }
}

.hot-news-area {
  margin: 18px 0 0 0;
  padding: 0 16px;
  
  .hot-title {
    font-size: 17px;
    font-weight: bold;
    margin-bottom: 12px;
    color: #111;
  }
  
  .hot-list {
    .hot-item {
      font-size: 15px;
      margin-bottom: 8px;
      padding: 8px 12px;
      background: #f8f8f8;
      border-radius: 8px;
      transition: background 0.3s ease;
      
      &:hover {
        background: #f0f0f0;
      }
      
      .hot-hash {
        color: #1bc47d;
        font-weight: bold;
        margin-right: 4px;
      }
    }
  }
}

.dynamic-feed-list {
  margin: 18px 0 0 0;
  flex: 1;
  
  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #111;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }
    
    p {
      color: #888;
      font-size: 14px;
    }
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    
    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }
    
    p {
      color: #888;
      font-size: 16px;
    }
  }
  
  .feed-card {
    display: flex;
    align-items: flex-start;
    padding: 16px 16px 12px 16px;
    border-bottom: 1px solid #f2f2f2;
    transition: background 0.3s ease;
    
    &:hover {
      background: #fafafa;
    }
    
    .feed-avatar {
      width: 38px;
      height: 38px;
      border-radius: 50%;
      margin-right: 10px;
      flex-shrink: 0;
      object-fit: cover;
    }
    
    .feed-main {
      flex: 1;
      min-width: 0;
      
      .feed-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 6px;
        
        .feed-nick {
          font-weight: bold;
          font-size: 16px;
          color: #111;
        }
        
        .feed-time {
          color: #888;
          font-size: 13px;
        }
      }
      
      .feed-content {
        font-size: 15px;
        line-height: 1.5;
        color: #333;
        margin-bottom: 8px;
        white-space: pre-line;
        word-wrap: break-word;
      }
      
      .feed-links {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 8px;
        
        .feed-link {
          color: #1bc47d;
          font-size: 14px;
          text-decoration: none;
          padding: 4px 8px;
          background: rgba(27, 196, 125, 0.1);
          border-radius: 4px;
          transition: background 0.3s ease;
          
          &:hover {
            background: rgba(27, 196, 125, 0.2);
          }
        }
      }
      
      .feed-img {
        width: 100%;
        max-width: 320px;
        border-radius: 12px;
        margin-top: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;
        object-fit: cover;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 480px) {
  .dynamic-page {
    padding-bottom: 70px;
  }
  
  .search-bar {
    margin: 12px 12px 0 12px;
    padding: 6px 10px;
    
    input {
      font-size: 14px;
    }
  }
  
  .main-tabs span {
    font-size: 15px;
    margin: 0 8px;
  }
  
  .dynamic-sub-tabs {
    margin: 16px 0 0 12px;
    gap: 12px;
    
    span {
      font-size: 15px;
      padding: 5px 18px;
    }
  }
  
  .hot-news-area {
    padding: 0 12px;
    
    .hot-list .hot-item {
      font-size: 14px;
      padding: 6px 10px;
    }
  }
  
  .dynamic-feed-list .feed-card {
    padding: 12px;
    
    .feed-avatar {
      width: 36px;
      height: 36px;
      margin-right: 10px;
    }
    
    .feed-main {
      .feed-header .feed-nick {
        font-size: 15px;
      }
      
      .feed-content {
        font-size: 14px;
      }
      
      .feed-links .feed-link {
        font-size: 13px;
      }
    }
  }
}
</style> 