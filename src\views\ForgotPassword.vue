<template>
  <div class="forgot-page">
    <div class="forgot-container">
      <router-link to="/login" class="back-btn">
        ← 返回登录
      </router-link>
      <h2 class="title">找回密码</h2>
      <form v-if="step === 1" @submit.prevent="handleSendCode" class="forgot-form">
        <div class="form-group">
          <label for="email">邮箱</label>
          <input id="email" v-model="email" type="email" placeholder="请输入注册邮箱" required />
        </div>
        <button class="send-btn" :disabled="!email || isLoading || countdown > 0" type="submit">
          <span v-if="!isLoading && countdown === 0">发送验证码</span>
          <span v-else-if="countdown > 0">重新发送 ({{ countdown }}s)</span>
          <span v-else class="loading-spinner"></span>
        </button>
      </form>
      <form v-else-if="step === 2" @submit.prevent="handleVerifyCode" class="forgot-form">
        <div class="form-group">
          <label for="code">验证码</label>
          <input id="code" v-model="code" type="text" maxlength="6" placeholder="请输入验证码" required />
        </div>
        <button class="send-btn" :disabled="!code || isLoading" type="submit">
          <span v-if="!isLoading">验证</span>
          <span v-else class="loading-spinner"></span>
        </button>
      </form>
      <form v-else-if="step === 3" @submit.prevent="handleResetPassword" class="forgot-form">
              <div class="form-group">
                <label for="newPassword">新密码</label>
                <input id="newPassword" v-model="newPassword" type="password" placeholder="请输入新密码" required />
                <div v-if="newPassword" class="password-strength">
                  <div class="strength-label">密码强度: <span :class="passwordStrengthClass">{{ passwordStrengthText }}</span></div>
                  <div class="strength-bar">
                    <div class="strength-indicator" :style="{ width: `${passwordStrength * 25}%`, backgroundColor: passwordStrengthColor }"></div>
                  </div>
                </div>
                <div class="password-tips" v-if="newPassword">
                  <div :class="{ 'tip-met': hasMinLength }">至少8个字符</div>
                  <div :class="{ 'tip-met': hasUpperCase }">包含大写字母</div>
                  <div :class="{ 'tip-met': hasLowerCase }">包含小写字母</div>
                  <div :class="{ 'tip-met': hasNumber }">包含数字</div>
                  <div :class="{ 'tip-met': hasSpecialChar }">包含特殊字符</div>
                </div>
              </div>
              <div class="form-group">
                <label for="confirmPassword">确认新密码</label>
                <input id="confirmPassword" v-model="confirmPassword" type="password" placeholder="请再次输入新密码" required />
                <div v-if="confirmPassword && newPassword !== confirmPassword" class="password-mismatch">
                  两次输入的密码不一致
                </div>
              </div>
        <button class="send-btn" :disabled="!canSubmitPassword || isLoading" type="submit">
          <span v-if="!isLoading">重置密码</span>
          <span v-else class="loading-spinner"></span>
        </button>
      </form>
      <div v-if="successMsg" class="success-msg">{{ successMsg }}</div>
      <div v-if="errorMsg" class="error-msg">{{ errorMsg }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import userApi from '@/api/user'

const router = useRouter()
const email = ref('')
const code = ref('')
const isLoading = ref(false)
const step = ref(1) // 1: 输入邮箱, 2: 输入验证码, 3: 输入新密码
const errorMsg = ref('')
const successMsg = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const countdown = ref(0)
let countdownTimer = null

// 密码强度检测
const hasMinLength = computed(() => newPassword.value.length >= 8)
const hasUpperCase = computed(() => /[A-Z]/.test(newPassword.value))
const hasLowerCase = computed(() => /[a-z]/.test(newPassword.value))
const hasNumber = computed(() => /[0-9]/.test(newPassword.value))
const hasSpecialChar = computed(() => /[!@#$%^&*(),.?":{}|<>]/.test(newPassword.value))

const passwordStrength = computed(() => {
  if (!newPassword.value) return 0
  
  let strength = 0
  if (hasMinLength.value) strength++
  if (hasUpperCase.value) strength++
  if (hasLowerCase.value) strength++
  if (hasNumber.value) strength++
  if (hasSpecialChar.value) strength++
  
  return strength
})

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value
  if (strength === 0) return '非常弱'
  if (strength === 1) return '弱'
  if (strength === 2) return '中'
  if (strength === 3) return '良好'
  if (strength === 4) return '强'
  return '非常强'
})

const passwordStrengthClass = computed(() => {
  const strength = passwordStrength.value
  if (strength <= 1) return 'strength-weak'
  if (strength <= 3) return 'strength-medium'
  return 'strength-strong'
})

const passwordStrengthColor = computed(() => {
  const strength = passwordStrength.value
  if (strength <= 1) return '#ff4d4f'
  if (strength === 2) return '#faad14'
  if (strength === 3) return '#52c41a'
  if (strength >= 4) return '#1890ff'
  return '#d9d9d9'
})

// 倒计时功能
const startCountdown = (seconds = 60) => {
  countdown.value = seconds
  clearInterval(countdownTimer)
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
    }
  }, 1000)
}

// 组件卸载时清除定时器
onUnmounted(() => {
  clearInterval(countdownTimer)
})

const canSubmitPassword = computed(() => {
  return newPassword.value && confirmPassword.value && newPassword.value === confirmPassword.value && newPassword.value.length >= 8
})

const handleSendCode = async () => {
  if (!email.value) return
  isLoading.value = true
  errorMsg.value = ''
  successMsg.value = ''
  try {
    const response = await userApi.sendEmailVerificationCode({ email: email.value })
    
    // 检查返回的code值
    if (response.code === 0) {
      // 失败情况
      errorMsg.value = response.msg || '发送验证码失败，请稍后重试'
    } else if (response.code === 1) {
      // 成功情况
      step.value = 2
      successMsg.value = `验证码已发送到邮箱：${email.value}`
      setTimeout(() => { successMsg.value = '' }, 3000)
      // 启动倒计时，60秒后才能重新发送
      startCountdown(60)
    } else {
      // 未知情况
      errorMsg.value = '未知错误，请稍后重试'
    }
  } catch (error) {
    errorMsg.value = error.response?.data?.message || '发送验证码失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

const handleVerifyCode = async () => {
  if (!code.value) return
  isLoading.value = true
  errorMsg.value = ''
  successMsg.value = ''
  try {
    const response = await userApi.verifyEmailCode({ email: email.value, code: code.value })
    
    // 检查返回的code值
    if (response.code === 0) {
      // 失败情况
      errorMsg.value = response.msg || '验证码验证失败，请检查后重试'
    } else if (response.code === 1) {
      // 成功情况
      step.value = 3
      successMsg.value = '验证码验证成功，请设置新密码'
      setTimeout(() => { successMsg.value = '' }, 2000)
    } else {
      // 未知情况
      errorMsg.value = '未知错误，请稍后重试'
    }
  } catch (error) {
    errorMsg.value = error.response?.data?.message || '验证码验证失败，请检查后重试'
  } finally {
    isLoading.value = false
  }
}

const handleResetPassword = async () => {
  if (!canSubmitPassword.value) return
  isLoading.value = true
  errorMsg.value = ''
  successMsg.value = ''
  try {
    const response = await userApi.resetPassword({ account: email.value, code: code.value, password: newPassword.value,passwordConfirm: confirmPassword.value })
    
    // 检查返回的code值
    if (response.code === 0) {
      // 失败情况
      errorMsg.value = response.msg || '密码重置失败，请稍后重试'
    } else if (response.code === 1) {
      // 成功情况
      successMsg.value = '密码重置成功，正在跳转...'
      setTimeout(() => {
        router.push('/mine')
      }, 1200)
    } else {
      // 未知情况
      errorMsg.value = '未知错误，请稍后重试'
    }
  } catch (error) {
    errorMsg.value = error.response?.data?.message || '密码重置失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped lang="scss">
.forgot-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}
.forgot-container {
  width: 100%;
  max-width: 400px;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 6px 24px rgba(0,0,0,0.10);
  padding: 36px 28px 28px 28px;
  position: relative;
}
.back-btn {
  display: inline-block;
  color: #666;
  text-decoration: none;
  font-size: 15px;
  margin-bottom: 18px;
  &:hover { color: #111; }
}
.title {
  font-size: 24px;
  font-weight: 700;
  color: #111;
  margin-bottom: 24px;
}
.forgot-form {
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  label {
    color: #333;
    font-size: 14px;
    font-weight: 600;
  }
  input {
    padding: 12px 14px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 16px;
    transition: border 0.2s;
    &:focus { border-color: #111; outline: none; }
  }
}
.send-btn {
  width: 100%;
  padding: 14px;
  background: linear-gradient(45deg, #111 0%, #333 100%);
  color: #fff;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12);
  }
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  to { transform: rotate(360deg); }
}
.error-msg {
  color: #dc3545;
  font-size: 14px;
  margin-top: 16px;
  text-align: center;
}
.success-msg {
  color: #1bc47d;
  font-size: 14px;
  margin-top: 16px;
  text-align: center;
}

/* 密码强度相关样式 */
.password-strength {
  margin-top: 10px;
}

.strength-label {
  font-size: 14px;
  margin-bottom: 5px;
}

.strength-weak {
  color: #ff4d4f;
}

.strength-medium {
  color: #faad14;
}

.strength-strong {
  color: #52c41a;
}

.strength-bar {
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.strength-indicator {
  height: 100%;
  transition: width 0.3s, background-color 0.3s;
}

.password-tips {
  margin-top: 10px;
  font-size: 12px;
  color: #999;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.password-tips div {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.tip-met {
  color: #52c41a;
  background-color: #f6ffed !important;
}

.password-mismatch {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 5px;
}
</style> 