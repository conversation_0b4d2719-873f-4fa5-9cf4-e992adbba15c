<template>
  <div class="help-page">
    <div class="top-bar">
      <div class="top-bar-back-row">
        <button class="top-bar-back" @click="goBack">←</button>
      </div>
      <div class="top-bar-title-row">
        <span class="top-bar-title">{{ plainTitle }}</span>
      </div>
    </div>
    <div class="help-search">
      <svg class="search-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#888" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="M21 21l-4.35-4.35"/></svg>
      <input class="search-input" :placeholder="$t('help.searchPlaceholder')" v-model="searchText" />
    </div>
    <div class="help-section">
      <div class="section-title">{{ $t('help.hotArticles') }}</div>
      <div class="topic-list">
        <div class="topic-item" v-for="(item, idx) in filteredTopics" :key="item.title" @click="goDetail(item, idx)">
          <span class="topic-icon">{{ item.icon }}</span>
          <span class="topic-title">{{ item.title }}</span>
          <span class="topic-arrow">›</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import articleApi from '@/api/article'
import { onMounted } from 'vue'

const { t } = useI18n()
const router = useRouter()
const goBack = () => router.back()
const goDetail = async (item, idx) => {
  // 假设接口返回的栏目有 id 字段，topics.value[idx] 对应当前栏目
  const cid = item.id || item.cid || item.categoryId || ''
  if (!cid) {
    // 如果没有 id 字段，无法请求
    console.warn('栏目ID不存在，无法获取文章列表')
    return
  }
  try {
    const res = await articleApi.getArticleListByCategory(cid)
    // 这里可以将文章列表传递到详情页，或做其他处理
    // 目前先跳转并传递cid
    router.push({ path: '/article-list', query: { cid, title: item.title } })
  } catch (e) {
    console.error('获取文章列表失败', e)
  }
}
const topics = ref([])
const searchText = ref('')
const filteredTopics = computed(() => {
  if (!searchText.value.trim()) return topics.value
  const keyword = searchText.value.trim().toLowerCase()
  return topics.value.filter(item =>
    (item.title || '').toLowerCase().includes(keyword)
  )
})
const loading = ref(true)
const error = ref(null)

const fetchTopics = async () => {
  loading.value = true
  error.value = null
  try {
    // 获取当前语言，兼容 zh-CN/en-US/zh_CN/en_US
    let lang = (localStorage.getItem('language') || 'zh-CN').replace('_', '-')
    // 后端需要 language 字段，格式如 zh-CN
    const res = await articleApi.getHelpCategories({ language: lang })
    // 假设返回数据结构为 { data: [{ icon, title }, ...] }
    topics.value = (res.data || []).map((item, idx) => ({
      icon: (idx + 1).toString(),
      title: item.name || '',
      id: item.id, // 添加 id 字段
      cid: item.cid, // 添加 cid 字段
      categoryId: item.categoryId // 添加 categoryId 字段
    }))
  } catch (e) {
    error.value = t('common.loading') + ' failed'
  } finally {
    loading.value = false
  }
}

onMounted(fetchTopics)

// 处理标题，去除 <br/> 等标签
const plainTitle = computed(() => {
  const raw = t('help.title')
  return raw.replace(/<br\s*\/?>/gi, ' ')
})
</script>
<style scoped lang="scss">
.help-page {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 32px;
}
.top-bar {
  display: flex;
  flex-direction: column;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid #f0f0f0;
  max-width: 480px;
  margin: 0 auto;
}
.top-bar-back-row {
  display: flex;
  align-items: center;
  height: 40px;
  position: relative;
}
.top-bar-title-row {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
}
.top-bar-back {
  margin-left: 8px;
  background: none;
  border: none;
  font-size: 22px;
  color: #222;
  cursor: pointer;
  padding: 0;
  height: 40px;
  display: flex;
  align-items: center;
}
.top-bar-title {
  font-size: 20px;
  font-weight: 800;
  color: #111;
  text-align: center;
  width: 100%;
}
.help-search {
  display: flex;
  align-items: center;
  background: #f3f5f7;
  border-radius: 22px;
  margin: 18px 18px 18px 18px;
  padding: 0 16px;
  height: 44px;
}
.search-icon {
  margin-right: 8px;
}
.search-input {
  border: none;
  background: transparent;
  outline: none;
  font-size: 16px;
  flex: 1;
  color: #222;
}
.help-section {
  max-width: 420px;
  margin: 0 auto;
  padding: 0 8px;
}
.section-title {
  font-size: 17px;
  font-weight: 700;
  color: #111;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.topic-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.topic-item {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  border-radius: 16px;
  padding: 10px 0 10px 0;
  cursor: pointer;
  transition: background 0.15s;
}
.topic-item:hover {
  background: #f3f5f7;
}
.topic-icon {
  width: 38px;
  height: 38px;
  background: #f3f5f7;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
  font-size: 20px;
}
.topic-title {
  flex: 1;
  font-size: 16px;
  color: #222;
  font-weight: 500;
}
.topic-arrow {
  color: #bbb;
  font-size: 22px;
  margin-left: 8px;
}
</style> 