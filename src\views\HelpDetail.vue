<template>
  <div class="help-detail-page">
    <div class="help-header">
      <button class="back-btn" @click="goBack">←</button>
    </div>
    <div class="help-title">{{ title }}</div>
    <div class="help-content">
      {{ $t('helpDetail.content', { title: title }) }}
    </div>
  </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const goBack = () => router.back()
const title = route.query.title || t('helpDetail.defaultTitle')
</script>

<style scoped lang="scss">
.help-detail-page {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 32px;
}
.help-header {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  padding: 18px 0 0 0;
}
.back-btn {
  background: none;
  border: none;
  font-size: 22px;
  color: #222;
  cursor: pointer;
  padding: 0 16px;
}
.help-title {
  font-size: 22px;
  font-weight: 800;
  color: #111;
  margin: 18px 0 18px 0;
  padding-left: 24px;
  line-height: 1.3;
}
.help-content {
  font-size: 16px;
  color: #444;
  margin: 0 24px;
  line-height: 1.7;
}
</style> 