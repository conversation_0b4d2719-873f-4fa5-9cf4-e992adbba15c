<template>
  <div class="home" :class="{ 
    'page-enter': true,
    'landscape': deviceInfo.isLandscape,
    'portrait': deviceInfo.isPortrait,
    'mobile': deviceInfo.isMobile,
    'tablet': deviceInfo.isTablet,
    'desktop': deviceInfo.isDesktop
  }">
    <div class="top-bar">
    </div>
    
    <!-- 装饰元素 -->
    <div class="decoration-circle circle-1"></div>
    <div class="decoration-circle circle-2"></div>
    
    <div class="main-content">
      <h1 :class="{ 'visible': titleVisible }" v-html="$t('home.title') + '<br/>' + $t('home.subtitle')"></h1>
      <p class="subtitle" :class="{ 'visible': subtitleVisible }">{{ $t('home.description') }}</p>
      <div class="btn-group" :class="{ 'visible': buttonsVisible }">
        <router-link to="/login" class="main-btn btn-grey" @click="createRipple">{{ $t('common.login') }}</router-link>
        <router-link to="/register" class="main-btn btn-black" @click="createRipple">{{ $t('common.register') }}</router-link>
      </div>
    </div>
    
    <TabBar class="tab-bar" />
  </div>
</template>

<script setup>
import TabBar from '../components/TabBar.vue'
import { onMounted, ref } from 'vue'
import { useDeviceState } from '../composables/useDeviceState'

// 获取设备状态
const { deviceInfo } = useDeviceState()

// 添加标题动画控制
const titleVisible = ref(false)
const subtitleVisible = ref(false)
const buttonsVisible = ref(false)

// 页面加载后触发动画
onMounted(() => {
  // 根据设备类型调整动画延迟
  const baseDelay = deviceInfo.value.isMobile ? 50 : 100
  
  setTimeout(() => { titleVisible.value = true }, baseDelay)
  setTimeout(() => { subtitleVisible.value = true }, baseDelay * 3)
  setTimeout(() => { buttonsVisible.value = true }, baseDelay * 5)
})

// 按钮点击波纹效果
const createRipple = (event) => {
  const button = event.currentTarget
  
  const circle = document.createElement('span')
  const diameter = Math.max(button.clientWidth, button.clientHeight)
  const radius = diameter / 2
  
  circle.style.width = circle.style.height = `${diameter}px`
  circle.style.left = `${event.clientX - button.offsetLeft - radius}px`
  circle.style.top = `${event.clientY - button.offsetTop - radius}px`
  circle.classList.add('ripple')
  
  const ripple = button.getElementsByClassName('ripple')[0]
  
  if (ripple) {
    ripple.remove()
  }
  
  button.appendChild(circle)
}
</script>

<style scoped lang="scss">
// 页面进入动画
.page-enter {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 响应式适配
@media screen and (max-width: 375px) {
  .main-content {
    margin-top: clamp(80px, 15vh, 100px);
    
    h1 {
      font-size: 26px;
    }
    
    .subtitle {
      font-size: 14px;
      margin-bottom: 36px;
    }
    
    .btn-group .main-btn {
      font-size: 16px;
      padding: 14px 0;
    }
  }
  
  .top-bar {
    padding: 16px 12px 0 12px;
    
    .dropdown-box {
      font-size: 14px;
      padding: 4px 20px 4px 12px;
    }
  }
}

@media screen and (min-width: 768px) {
  .main-content {
    h1 {
      font-size: 36px;
      letter-spacing: -0.03em;
    }
    
    .subtitle {
      font-size: 18px;
      margin-bottom: 56px;
    }
    
    .btn-group {
      max-width: 400px;
      
      .main-btn {
        padding: 18px 0;
      }
    }
  }
}

@media screen and (min-width: 768px) and (max-width: 1024px) {
  .main-content, .home-card {
    max-width: 600px;
    margin: 0 auto;
    font-size: 18px;
  }
  h1, .main-title {
    font-size: 48px;
  }
  .btn-group .main-btn, .home-btn {
    font-size: 22px;
    padding: 22px 0;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1366px) {
  .main-content, .home-card {
    max-width: 800px;
    margin: 0 auto;
    font-size: 20px;
  }
  h1, .main-title {
    font-size: 56px;
  }
  .btn-group .main-btn, .home-btn {
    font-size: 24px;
    padding: 26px 0;
  }
}
@media screen and (min-width: 1367px) {
  .main-content, .home-card {
    max-width: 1000px;
    margin: 0 auto;
    font-size: 22px;
  }
  h1, .main-title {
    font-size: 64px;
  }
}

  // 横屏适配
@media screen and (orientation: landscape) and (max-height: 600px) {
  .home {
    padding-bottom: 50px;
    display: flex;
    flex-direction: row;
    
    &.landscape {
      .main-content {
        width: 60%;
        margin-left: auto;
        padding-right: 5%;
      }
      
      .top-bar {
        position: absolute;
        width: 100%;
        z-index: 10;
      }
    }
  }
  
  .main-content {
    margin-top: 60px;
    justify-content: center;
    
    h1 {
      font-size: 24px;
      margin-bottom: 8px;
      text-align: left;
    }
    
    .subtitle {
      margin-bottom: 24px;
      text-align: left;
    }
    
    .btn-group {
      flex-direction: row;
      
      .main-btn {
        padding: 12px 0;
      }
    }
  }
  
  .decoration-circle {
    &.circle-1 {
      right: auto;
      left: 10%;
      top: 50%;
      transform: translateY(-50%);
    }
    
    &.circle-2 {
      display: none;
    }
  }
}

// 设备特定适配
.home {
  &.landscape.mobile {
    .main-content {
      margin-top: 40px;
    }
    
    .btn-group {
      max-width: 100%;
    }
  }
  
  &.tablet {
    .main-content {
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .btn-group {
      max-width: 450px;
    }
  }
  
  &.desktop {
    .main-content {
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .btn-group {
      max-width: 500px;
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .home {
    background: linear-gradient(to bottom, #1a1a1a, #111);
    
    &::before {
      background: linear-gradient(135deg, rgba(26,26,26,0.8), rgba(17,17,17,0.8));
    }
    
    .main-content {
      h1 {
        background: linear-gradient(135deg, #fff, #ccc);
        -webkit-background-clip: text;
      }
      
      .subtitle {
        color: #999;
      }
      
      .btn-group {
        .btn-grey {
          background: #2a2a2a;
          color: #ccc;
          
          &:hover {
            background: #333;
            color: #fff;
          }
        }
        
        .btn-black {
          background: linear-gradient(135deg, #fff, #ccc);
          color: #111;
        }
      }
    }
    
    .top-bar {
      .icon-btn svg {
        fill: #fff;
      }
      
      .dropdown-box {
        background: rgba(42, 42, 42, 0.8);
        color: #fff;
      }
    }
  }
}

.home {
  min-height: 100vh;
  background: linear-gradient(to bottom, #fff, #f8f9fa);
  display: flex;
  flex-direction: column;
  padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px));
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,249,250,0.8));
    z-index: 0;
  }
  
  // 装饰元素样式
  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.05;
    background: linear-gradient(135deg, #000, transparent);
    z-index: 0;
  }
  
  .circle-1 {
    width: 300px;
    height: 300px;
    top: -100px;
    right: -100px;
    animation: float 15s infinite ease-in-out;
  }
  
  .circle-2 {
    width: 200px;
    height: 200px;
    bottom: 100px;
    left: -80px;
    animation: float 20s infinite ease-in-out reverse;
  }
  
  @keyframes float {
    0%, 100% {
      transform: translate(0, 0);
    }
    50% {
      transform: translate(20px, 20px);
    }
  }
}

// 优化TabBar样式
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  padding-bottom: env(safe-area-inset-bottom, 0px);
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
.top-bar {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 18px 16px 0 16px;
  position: relative;
  z-index: 1;
  
  .icon-btn {
    background: none;
    border: none;
    padding: 8px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.2s ease;
    
    svg {
      fill: #222;
      transition: fill 0.2s ease;
    }
    
    &:hover {
      background-color: rgba(0,0,0,0.05);
      
      svg {
        fill: #000;
      }
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
  
  .dropdown-box {
    background: rgba(242, 242, 242, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 6px 24px 6px 16px;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #222;
    font-weight: 500;
    margin: 0 auto;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(242, 242, 242, 0.9);
      box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }
    
    .arrow {
      margin-left: 6px;
      transition: transform 0.3s ease;
    }
    
    &:hover .arrow {
      transform: rotate(180deg);
    }
  }
}
  .main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  margin-top: clamp(140px, 25vh, 180px);
  padding: 0 20px;
  position: relative;
  z-index: 1;
  
  h1 {
    font-size: clamp(28px, 5vw, 32px);
    font-weight: 800;
    text-align: center;
    margin-bottom: 16px;
    line-height: 1.3;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, #000, #333);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
    
    &.visible {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .subtitle {
    color: #666;
    font-size: 16px;
    margin-bottom: 48px;
    text-align: center;
    font-weight: 400;
    letter-spacing: 0.02em;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
    
    &.visible {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .btn-group {
    display: flex;
    gap: 16px;
    width: 100%;
    max-width: 340px;
    margin: 0 auto;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
    
    &.visible {
      opacity: 1;
      transform: translateY(0);
    }
    
    .main-btn {
      flex: 1;
      border-radius: 16px;
      font-size: 18px;
      padding: 16px 0;
      text-align: center;
      font-weight: 600;
      border: none;
      outline: none;
      text-decoration: none;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(rgba(255,255,255,0.1), rgba(255,255,255,0));
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      
      &:hover {
        transform: translateY(-2px);
        
        &::before {
          opacity: 1;
        }
      }
      
      &:active {
        transform: translateY(0);
      }
      
      // 波纹效果
      position: relative;
      overflow: hidden;
      
      .ripple {
        position: absolute;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.4);
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
      }
    }
    
    @keyframes ripple {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
    
    .btn-grey {
      background: #f5f5f5;
      color: #666;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      
      &:hover {
        background: #f8f8f8;
        color: #333;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
      }
    }
    
    .btn-black {
      background: linear-gradient(135deg, #000, #222);
      color: #fff;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      }
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 