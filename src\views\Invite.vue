<template>
  <div class="invite-page">
    <div class="invite-header">
      <button class="back-btn" @click="goBack">←</button>
      <span class="invite-icon">
        <svg width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="#222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="4"/><path d="M9 9h6v6H9z"/></svg>
      </span>
    </div>
    <div class="invite-warning">
      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#b26a00" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 8v4"/><circle cx="12" cy="16" r="1"/></svg>
      <span>{{ $t('invite.warning') }}</span>
      <button class="auth-btn">{{ $t('invite.authBtn') }}</button>
    </div>
    <div class="invite-title">
      {{ $t('invite.title', { amount: '10 USDT' }) }}
    </div>
    <div class="invite-sub">
      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#888" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 8v4"/><circle cx="12" cy="16" r="1"/></svg>
      <span>{{ $t('invite.subtitle') }}</span>
    </div>
    <div class="invite-desc">
      {{ $t('invite.description') }}
    </div>
    <div class="invite-rule">
      <router-link to="/invite-rule" class="rule-link">{{ $t('invite.ruleLink') }}</router-link>
    </div>
    <div class="invite-actions">
      <div
        class="action-btn"
        v-for="btn in btnList"
        :key="btn.label"
        :style="{flex: 1}"
        @click="handleAction(btn)"
      >
        <span class="action-icon" v-html="btn.icon"></span>
        <span class="action-label">{{ btn.label }}</span>
      </div>
    </div>
    <!-- QR Code Modal -->
    <div v-if="showQr" class="qr-modal-mask" @click.self="closeQrModal">
      <div class="qr-modal">
        <div class="qr-modal-header">
          <h3>{{ qrType === 'code' ? $t('invite.modal.qrCode') : $t('invite.modal.qrLink') }}</h3>
          <button class="close-btn" @click="closeQrModal">×</button>
        </div>
        <img :src="qrCodeUrl" :alt="$t('invite.modal.qrCode')" class="qr-img" />
        <div class="qr-code-text">
          <template v-if="qrType === 'code'">
            {{ $t('invite.modal.inviteCode', { code: inviteCode }) }}
            <button class="copy-btn" @click="copyCode">{{ copySuccess ? $t('invite.modal.copied') : $t('invite.modal.copyCode') }}</button>
          </template>
          <template v-else>
            <div class="invite-link-copy">
              <span class="invite-link">{{ inviteLink }}</span>
              <button class="copy-btn" @click="copyLink">{{ copySuccess ? $t('invite.modal.copied') : $t('invite.modal.copyLink') }}</button>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import QRCode from 'qrcode'

const USER_CACHE_KEY = 'user_cache'
function getInviteCodeFromCache() {
  const cached = localStorage.getItem(USER_CACHE_KEY)
  if (cached) {
    try {
      const parsed = JSON.parse(cached)
      return parsed.invitation_code || ''
    } catch (e) {
      console.error('解析缓存用户数据失败', e)
    }
  }
  return ''
}

const { t } = useI18n()
const router = useRouter()
const goBack = () => router.back()
const btnList = [
  { 
    label: t('invite.buttons.telegram'), 
    icon: '<svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="12" fill="#0088cc"/><path d="M8 12l3 3 5-7" stroke="#fff" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/><polygon points="8,12 11,15 16,8" fill="#fff"/></svg>', 
    link: 'https://t.me/your_telegram_group',
    type: 'telegram'
  },
  { 
    label: t('invite.buttons.inviteCode'), 
    icon: '<svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="4" y="4" width="16" height="16" rx="4"/><path d="M8 12h8"/></svg>',
    type: 'code'
  },
  { 
    label: t('invite.buttons.inviteLink'), 
    icon: '<svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10 13a5 5 0 0 1 7 7l-1 1a5 5 0 0 1-7-7l1-1"/><path d="M14 11a5 5 0 0 0-7-7l-1 1a5 5 0 0 0 7 7l1-1"/></svg>',
    type: 'link'
  },
]
const showQr = ref(false)
const qrCodeUrl = ref('')
const inviteCode = getInviteCodeFromCache()
const inviteLink = `https://yourdomain.com/register?code=${inviteCode}`
const qrType = ref('') // 'code' or 'link'
const copySuccess = ref(false)

async function openQrModal(type) {
  qrType.value = type
  showQr.value = true
  
  // Generate QR code based on type
  if (type === 'code') {
    qrCodeUrl.value = await QRCode.toDataURL(inviteCode, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
  } else {
    qrCodeUrl.value = await QRCode.toDataURL(inviteLink, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
  }
  copySuccess.value = false
}

function closeQrModal() {
  showQr.value = false
}

async function copyCode() {
  try {
    await navigator.clipboard.writeText(inviteCode)
    copySuccess.value = true
    setTimeout(() => { copySuccess.value = false }, 1500)
  } catch (err) {
    console.error('Failed to copy code:', err)
  }
}

async function copyLink() {
  try {
    await navigator.clipboard.writeText(inviteLink)
    copySuccess.value = true
    setTimeout(() => { copySuccess.value = false }, 1500)
  } catch (err) {
    console.error('Failed to copy link:', err)
  }
}

function handleAction(btn) {
  if (btn.type === 'telegram') {
    window.open(btn.link, '_blank')
  } else if (btn.type === 'code') {
    openQrModal('code')
  } else if (btn.type === 'link') {
    openQrModal('link')
  }
}
</script>
<style scoped lang="scss">
.invite-page {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 32px;
}
.invite-header {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 0 0 0;
}
.back-btn {
  background: none;
  border: none;
  font-size: 22px;
  color: #222;
  cursor: pointer;
  padding: 0 16px;
}
.invite-icon {
  margin-right: 18px;
}
.invite-warning {
  display: flex;
  align-items: center;
  background: #ffc940;
  color: #b26a00;
  font-size: 15px;
  border-radius: 10px;
  padding: 12px 16px;
  margin: 18px 18px 0 18px;
  position: relative;
  gap: 8px;
}
.auth-btn {
  background: none;
  border: none;
  color: #b26a00;
  font-size: 15px;
  font-weight: 600;
  margin-left: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
}
.invite-title {
  font-size: 26px;
  font-weight: 800;
  color: #111;
  margin: 24px 0 10px 0;
  padding-left: 24px;
  line-height: 1.3;
}
.highlight {
  color: #228c22;
}
.invite-sub {
  display: flex;
  align-items: center;
  color: #888;
  font-size: 15px;
  margin-left: 24px;
  margin-bottom: 10px;
  gap: 6px;
}
.invite-desc {
  color: #666;
  font-size: 16px;
  margin: 0 24px 10px 24px;
}
.invite-rule {
  margin: 0 24px 18px 24px;
}
.rule-link {
  color: #111;
  font-weight: 700;
  text-decoration: underline;
  font-size: 16px;
}
.invite-actions {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  margin: 36px 0 0 0;
  padding: 0 8px;
  gap: 0;
}
.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: auto;
  min-width: 0;
  text-decoration: none;
  color: #222;
  background: none;
  box-shadow: none;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(0);
  }
}
.action-icon {
  width: 56px;
  height: 56px;
  background: #111;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}
.action-label {
  font-size: 13px;
  margin-top: 4px;
  font-weight: 400;
  color: #222;
  letter-spacing: 0.2px;
}
.qr-modal-mask {
  position: fixed; 
  left: 0; 
  top: 0; 
  right: 0; 
  bottom: 0;
  background: rgba(0,0,0,0.45);
  display: flex; 
  align-items: center; 
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}
.qr-modal {
  background: #fff;
  border-radius: 20px;
  padding: 0;
  box-shadow: 0 12px 40px rgba(0,0,0,0.25);
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 90%;
  width: 320px;
  overflow: hidden;
}
.qr-modal-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #222;
  }
  
  .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    
    &:hover {
      background: #f5f5f5;
      color: #666;
    }
  }
}
.qr-img {
  width: 200px;
  height: 200px;
  margin: 24px 0 20px 0;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}
.qr-code-text {
  font-size: 16px;
  color: #222;
  margin-bottom: 24px;
  font-weight: 600;
  text-align: center;
  padding: 0 24px;
}
.invite-link-copy {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}
.invite-link {
  font-size: 14px;
  color: #1bc47d;
  word-break: break-all;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  font-family: monospace;
}
.copy-btn {
  background: #1bc47d;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #16a367;
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}
</style> 