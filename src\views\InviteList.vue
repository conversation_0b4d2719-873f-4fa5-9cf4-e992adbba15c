<template>
  <div class="invite-list-page" 
       @touchstart="touchStart" 
       @touchmove="touchMove" 
       @touchend="touchEnd">
    <div class="pull-down-indicator" v-if="isPulling">
      <div class="pull-down-spinner" :class="{ 'rotate': isRefreshing }"></div>
      <div class="pull-down-text">{{ isRefreshing ? $t('inviteList.refreshing') : $t('inviteList.pullToRefresh') }}</div>
    </div>
    
    <div class="invite-header">
      <span class="back-btn" @click="goBack">
        <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15 18l-6-6 6-6"/></svg>
      </span>
      <span class="invite-title">{{ $t('inviteList.title') }}</span>
      <!-- 删除右侧搜索按钮 -->
    </div>
    <div class="search-bar" style="padding: 12px 16px;">
      <input
        type="text"
        class="search-input"
        :placeholder="$t('inviteList.searchPlaceholder')"
        v-model="searchQuery"
        @input="onSearchInput"
        style="width: 100%; padding: 8px 32px 8px 12px; border-radius: 8px; border: 1px solid #eee; font-size: 16px;"
      />
      <span v-if="searchQuery" class="clear-search" @click="clearSearch" style="position:absolute;right:28px;top:50%;transform:translateY(-50%);cursor:pointer;font-size:18px;color:#bbb;">×</span>
    </div>
    <div v-if="isSearching">
      <div class="invite-list" v-if="searchResult.length > 0">
        <div class="invite-item" v-for="item in searchResult" :key="item.id" @click="viewInviteDetail(item)">
          <span class="invite-avatar-bg">
            <img class="invite-avatar" :src="item.avatar" alt="avatar" />
          </span>
          <div class="invite-info">
            <div class="invite-name">{{ item.name }}</div>
            <div class="invite-tag">{{ getRoleDisplay(item) }}</div>
          </div>
          <span class="invite-reward">{{ item.reward }} USDT</span>
          <span class="invite-arrow">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 6l6 6-6 6"/></svg>
          </span>
        </div>
      </div>
      <div v-else-if="searchLoading" class="loading-text" style="text-align:center;padding:12px 0;">{{ $t('common.loading') }}</div>
      <div v-else class="empty-state">
        <div class="empty-icon">🔍</div>
        <div class="empty-text">{{ $t('inviteList.noRecords') }}</div>
        <div class="empty-subtext">{{ $t('inviteList.emptySubtext') }}</div>
      </div>
    </div>
    <div class="invite-stats">
      <div class="invite-main-title" v-html="$t('inviteList.stats', { count: inviteCount, reward: totalReward, gift: '<span class=\'gift-emoji\'>🎁</span>' })"></div>
      <div class="invite-sub-title">
        {{ $t('inviteList.continueInvite') }}
      </div>
    </div>
    <!-- 加载状态 -->
    <div class="loading-state" v-if="loading">
      <div class="loading-spinner"></div>
              <div class="loading-text">{{ $t('common.loading') }}</div>
    </div>
    
    <!-- 邀请列表 -->
    <div class="invite-list" v-else-if="filteredList.length > 0">
      <div class="invite-item" v-for="item in filteredList" :key="item.id" @click="viewInviteDetail(item)">
        <span class="invite-avatar-bg">
          <img class="invite-avatar" :src="item.avatar" alt="avatar" />
        </span>
        <div class="invite-info">
          <div class="invite-name">{{ item.name }}</div>
          <div class="invite-tag">{{ getRoleDisplay(item) }}</div>
        </div>
        <span class="invite-reward">{{ item.reward }} USDT</span>
        <span class="invite-arrow">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 6l6 6-6 6"/></svg>
        </span>
      </div>
      <div v-if="inviteLoading" class="loading-text" style="text-align:center;padding:12px 0;">{{ $t('common.loading') }}</div>
      <div v-if="inviteFinished && filteredList.length > 0" class="invite-no-more">{{ $t('common.noMore') }}</div>
    </div>
    
    <!-- 空状态 -->
    <div class="empty-state" v-else>
      <div class="empty-icon">🔍</div>
              <div class="empty-text">{{ $t('inviteList.noRecords') }}</div>
              <div class="empty-subtext">{{ $t('inviteList.emptySubtext') }}</div>
    </div>
    <div class="invite-bottom-bar">
      <button class="invite-share-btn" @click="shareInvite">
        <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 19V5"/><path d="M5 12l7-7 7 7"/></svg>
        <span>{{ $t('inviteList.shareInviteLink') }}</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import userApi from '../api/user'
import { useI18n } from 'vue-i18n'
const router = useRouter()
const toast = useToast()
const { t } = useI18n()
// 状态变量
const loading = ref(true)
const isPulling = ref(false)
const isRefreshing = ref(false)
const startY = ref(0)
const pullDistance = ref(0)
const pullThreshold = 80 // 下拉刷新阈值

const searchQuery = ref('')
const isSearching = ref(false)
const searchLoading = ref(false)
const searchResult = ref([])
let searchTimeout = null

const USER_CACHE_KEY = 'user_cache'
function getBelong() {
  const cached = localStorage.getItem(USER_CACHE_KEY)
  if (cached) {
    try {
      const parsed = JSON.parse(cached)
      return parsed.id || parsed.sn
    } catch (e) {}
  }
  return ''
}
const belong = getBelong()

// 邀请列表数据
const inviteList = ref([])
const invitePage = ref(1)
const invitePageSize = 10
const inviteLoading = ref(false)
const inviteFinished = ref(false)
const inviteError = ref(false)


const filteredList = computed(() => {
  if (!searchQuery.value) return inviteList.value
  const q = searchQuery.value.trim().toLowerCase()
  return inviteList.value.filter(item =>
    (item.name && item.name.toLowerCase().includes(q)) ||
    (item.account && item.account.toLowerCase().includes(q))
  )
})

// 角色显示文本
function getRoleDisplay(item) {
  console.log(item)
  if (item.role === '2') {
    return t('mine.broker') + 'V' + (item.levle || item.level || 1)
  } else {
    return t('mine.user')
  }
}

// 监听页面滚动触底自动加载下一页
function handleScroll() {
  if (inviteLoading.value || inviteFinished.value || loading.value) return
  const scrollTop = window.scrollY || document.documentElement.scrollTop
  const windowHeight = window.innerHeight
  const docHeight = document.documentElement.scrollHeight
  if (docHeight - (scrollTop + windowHeight) < 80) {
    fetchInviteList()
  }
}

async function fetchInviteList(reset = false) {
  if (inviteLoading.value || inviteFinished.value) return
  if (reset) {
    invitePage.value = 1
    inviteFinished.value = false
    loading.value = true // 首次加载或刷新时显示主 loading
  }
  inviteLoading.value = true
  inviteError.value = false
  try {
    const res = await userApi.listByBelong({
      page: invitePage.value,
      pageSize: invitePageSize,
      belong
    })
    // 适配后端数据结构
    const list = (res.data?.lists || []).map(item => ({
      id: item.id,
      role: item.role,
      avatar: item.avatar,
      name: item.realname || item.nickname || item.account,
      level: item.levle, // 注意接口字段拼写
      reward: 0 // 接口没有奖励字段，默认0
    }))
    if (reset) inviteList.value = []
    if (list.length > 0) {
      inviteList.value = reset ? list : inviteList.value.concat(list)
      if (list.length < invitePageSize) inviteFinished.value = true
      else invitePage.value++
    } else {
      if (invitePage.value === 1) inviteList.value = []
      inviteFinished.value = true
    }
  } catch (e) {
    inviteError.value = true
  } finally {
    inviteLoading.value = false
    if (loading.value) loading.value = false // 只在首次加载或刷新后关闭主 loading
  }
}

function onInviteScroll(e) {
  const el = e.target
  if (el.scrollHeight - el.scrollTop - el.clientHeight < 50) {
    fetchInviteList()
  }
}

// 初始化
onMounted(() => {
  fetchInviteList(true)
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

// 计算属性
const totalReward = computed(() => {
  return inviteList.value.reduce((sum, item) => sum + item.reward, 0)
})

const inviteCount = computed(() => {
  return inviteList.value.length
})

// 方法
function goBack() {
  router.back()
}

function shareInvite() {
  // 生成邀请链接
  const inviteLink = 'https://example.com/invite/123456'
  
  // 检查是否支持原生分享API
  if (navigator.share) {
    navigator.share({
      title: t('inviteList.shareTitle'),
      text: t('inviteList.shareText'),
      url: inviteLink,
    })
    .then(() => toast.success(t('inviteList.shareSuccess')))
    .catch((error) => {
      console.error('分享失败:', error)
      // 回退到剪贴板复制
      copyToClipboard(inviteLink)
    })
  } else {
    // 回退到剪贴板复制
    copyToClipboard(inviteLink)
  }
}

function copyToClipboard(text) {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text)
      .then(() => toast.success(t('inviteList.copySuccess')))
      .catch(() => {
        toast.error(t('inviteList.copyError'))
        console.error(t('inviteList.copyError'))
      })
  } else {
    // 兼容性处理
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    
    try {
      const successful = document.execCommand('copy')
      if (successful) {
        toast.success(t('inviteList.copySuccess'))
      } else {
        toast.error(t('inviteList.copyError'))
      }
    } catch (err) {
      toast.error(t('inviteList.copyError'))
      console.error(t('inviteList.copyError'), err)
    }
    
    document.body.removeChild(textArea)
  }
}

function viewInviteDetail(item) {
  // 跳转到邀请详情页面
  router.push(`/invite/detail/${item.id}`)
}

// 下拉刷新相关方法
function touchStart(e) {
  // 只有在页面顶部才启用下拉刷新
  if (window.scrollY === 0) {
    startY.value = e.touches[0].clientY
    isPulling.value = true
  }
}

function touchMove(e) {
  if (!isPulling.value || loading.value || isRefreshing.value) return
  
  const currentY = e.touches[0].clientY
  const diff = currentY - startY.value
  
  // 只处理下拉操作
  if (diff > 0) {
    // 添加阻尼效果，使下拉感觉更自然
    pullDistance.value = Math.pow(diff, 0.8)
    
    // 防止页面滚动
    e.preventDefault()
  }
}

function touchEnd() {
  if (!isPulling.value) return
  
  if (pullDistance.value > pullThreshold && !loading.value && !isRefreshing.value) {
    // 触发刷新
    refreshInviteList()
  }
  
  // 重置状态
  setTimeout(() => {
    isPulling.value = false
    pullDistance.value = 0
  }, 300)
}

// 刷新邀请列表
async function refreshInviteList() {
  if (loading.value || isRefreshing.value) return
  
  isRefreshing.value = true
  
  try {
    // 模拟网络请求延迟
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 重新获取数据
    await fetchInviteList(true)
    
    // 显示成功提示
    toast.success(t('inviteList.refreshSuccess'))
  } catch (error) {
    console.error('刷新邀请列表失败:', error)
    toast.error(t('inviteList.refreshError'))
  } finally {
    isRefreshing.value = false
  }
}

function searchInvites() {
  toast.info(t('inviteList.searchDeveloping'))
}

// 搜索功能
function onSearchInput(e) {
  searchQuery.value = e.target.value
}

function clearSearch() {
  searchQuery.value = ''
}
</script>

<style lang="scss" scoped>
@use '@/styles/_variables.scss' as *;

.invite-list-page {
  background: $secondary;
  min-height: 100vh;
  padding-bottom: 80px;
}

.invite-header {
  display: flex;
  align-items: center;
  height: 56px;
  padding: 0 $spacing-md;
  background: $secondary;
  border-bottom: 1px solid $gray-100;
  position: sticky;
  top: 0;
  z-index: 10;
}

.back-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-right: $spacing-xs;
  color: $gray-500;
  
  &:active {
    color: $gray-700;
  }
}

.invite-title {
  font-size: $font-size-lg;
  color: $gray-900;
  font-weight: $font-weight-semibold;
  margin-left: $spacing-xs;
  flex: 1;
}

.search-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: $gray-500;
  
  &:active {
    color: $gray-700;
  }
}

.search-bar {
  position: sticky;
  top: 56px; /* Adjust based on header height */
  z-index: 9;
  background: $secondary;
  padding: $spacing-md $spacing-lg;
  border-bottom: 1px solid $gray-100;
}

.search-input {
  width: 100%;
  padding: $spacing-xs $spacing-md;
  border: 1px solid $gray-200;
  border-radius: $radius-md;
  font-size: $font-size-base;
  color: $gray-900;
  background-color: $gray-50;
  box-shadow: $shadow-xs;
  transition: border-color $transition-fast;

  &:focus {
    outline: none;
    border-color: $primary;
    box-shadow: $shadow-sm;
  }
}

.clear-search {
  position: absolute;
  right: $spacing-md;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: $gray-400;
  font-size: $font-size-lg;
}

.invite-stats {
  padding: $spacing-lg $spacing-lg $spacing-md;
}

.invite-main-title {
  font-size: 20px;
  font-weight: $font-weight-bold;
  color: $gray-900;
  line-height: 1.4;
  
  .highlight {
    color: $primary;
  }
  
  .gift-emoji {
    font-size: 20px;
    margin-left: $spacing-xs;
  }
}

.invite-sub-title {
  font-size: $font-size-sm;
  color: $gray-600;
  margin-top: $spacing-sm;
}
.invite-list {
  margin: 0;
  display: flex;
  flex-direction: column;
  background: $secondary;
  border-radius: $radius-lg;
  overflow: hidden;
  box-shadow: $shadow-sm;
  @scroll.passive="onInviteScroll"
}

.invite-item {
  display: flex;
  align-items: center;
  padding: $spacing-md $spacing-lg;
  border-bottom: 1px solid $gray-100;
  background: $secondary;
  transition: background-color $transition-fast;
  
  &:active {
    background-color: $gray-50;
  }
  
  &:last-child {
    border-bottom: none;
  }
}

.invite-avatar-bg {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: $success-light;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $spacing-md;
  box-shadow: $shadow-xs;
}

.invite-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.invite-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.invite-name {
  font-size: $font-size-base;
  color: $gray-900;
  font-weight: $font-weight-semibold;
}

.invite-tag {
  font-size: $font-size-xs;
  color: $warning;
  font-weight: $font-weight-medium;
  display: inline-block;
}

.invite-reward {
  font-size: $font-size-sm;
  color: $success;
  font-weight: $font-weight-medium;
  margin-right: $spacing-sm;
  min-width: 70px;
  text-align: right;
}

.invite-arrow {
  color: $gray-400;
  display: flex;
  align-items: center;
  transition: transform $transition-fast;
  
  .invite-item:active & {
    transform: translateX(2px);
  }
}
.invite-bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: $secondary;
  box-shadow: $shadow-up-lg;
  padding: $spacing-md 0;
  display: flex;
  justify-content: center;
  z-index: 20;
}

.invite-share-btn {
  width: 90%;
  max-width: 400px;
  background: linear-gradient(90deg, $primary, $primary-dark);
  color: $secondary;
  border: none;
  border-radius: $border-radius-full;
  padding: $spacing-md 0;
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-sm;
  cursor: pointer;
  box-shadow: $shadow-md;
  transition: transform $transition-fast, box-shadow $transition-fast;
  
  &:active {
    transform: scale(0.98);
    box-shadow: $shadow-sm;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-2xl $spacing-lg;
  background-color: $secondary;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: $spacing-md;
    opacity: 0.8;
  }
  
  .empty-text {
    font-size: $font-size-base;
    color: $gray-800;
    font-weight: $font-weight-medium;
    margin-bottom: $spacing-xs;
  }
  
  .empty-subtext {
    font-size: $font-size-sm;
    color: $gray-600;
    text-align: center;
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-2xl $spacing-lg;
  background-color: $secondary;
  min-height: 200px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid $gray-100;
  border-top: 3px solid $primary;
  border-radius: 50%;
  margin-bottom: $spacing-md;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: $font-size-base;
  color: $gray-600;
  font-weight: $font-weight-medium;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.pull-down-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-md;
  background-color: rgba($secondary, 0.8);
  z-index: 10;
  transform: translateY(calc(-100% + v-bind(pullDistance + 'px')));
  transition: transform 0.3s ease;
}

.pull-down-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid $gray-100;
  border-top: 2px solid $primary;
  border-radius: 50%;
  margin-bottom: $spacing-xs;
  
  &.rotate {
    animation: spin 1s linear infinite;
  }
}

.pull-down-text {
  font-size: $font-size-sm;
  color: $gray-600;
}

.invite-no-more {
  text-align: center;
  color: #bbb;
  padding: 12px 0;
  font-size: 14px;
}
</style> 