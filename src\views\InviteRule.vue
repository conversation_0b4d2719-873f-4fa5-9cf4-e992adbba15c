<template>
  <div class="invite-rule-page">
    <div class="top-bar">
      <button class="back-btn" @click="goBack">←</button>
      <span class="top-title">{{ $t('inviteRule.title') }}</span>
    </div>
    <div class="tabs">
      <span :class="['tab', activeTab === 'rule' ? 'active' : '']" @click="activeTab = 'rule'">{{ $t('inviteRule.ruleTab') }}</span>
      <span :class="['tab', activeTab === 'faq' ? 'active' : '']" @click="activeTab = 'faq'">{{ $t('inviteRule.faqTab') }}</span>
    </div>
    <div v-if="activeTab === 'rule'">
      <div class="section">
        <div class="section-title">
          {{ $t('inviteRule.deadline') }}
        </div>
        <div class="steps">
          <div class="step">
            <span class="icon">🔗</span>
            <span>{{ $t('inviteRule.step1') }}</span>
          </div>
          <div class="step">
            <span class="icon">👤</span>
            <span>{{ $t('inviteRule.step2') }}</span>
          </div>
          <div class="step">
            <span class="icon">⏬</span>
            <span>{{ $t('inviteRule.step3') }}</span>
          </div>
        </div>
      </div>
      <div class="section">
        <div class="section-title">{{ $t('inviteRule.myReward') }}</div>
        <div class="section-content">
          {{ $t('inviteRule.myRewardDesc') }}
        </div>
      </div>
      <div class="section">
        <div class="section-title">{{ $t('inviteRule.friendReward') }}</div>
        <div class="section-content">
          {{ $t('inviteRule.friendRewardDesc') }}
        </div>
      </div>
    </div>
    <div v-else>
      <div class="section" v-for="(item, idx) in faqList" :key="idx">
        <div class="section-title">{{ item.q }}</div>
        <div class="section-content">{{ item.a }}</div>
      </div>
    </div>
    <div class="footer-link" @click="goTerms">
      <span>{{ $t('inviteRule.terms') }}</span>
      <span class="arrow">&gt;</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const activeTab = ref('rule')
const router = useRouter()
const goBack = () => router.back()
const goTerms = () => {
  router.push('/invite-rule-terms')
}
const faqList = [
  { q: t('inviteRule.faq1.q'), a: t('inviteRule.faq1.a') },
  { q: t('inviteRule.faq2.q'), a: t('inviteRule.faq2.a') },
  { q: t('inviteRule.faq3.q'), a: t('inviteRule.faq3.a') },
]
</script>

<style scoped lang="scss">
.invite-rule-page {
  background: #fff;
  min-height: 100vh;
  padding-bottom: 32px;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
}
.top-bar {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 18px 0 0 0;
  background: #fff;
}
.back-btn {
  background: none;
  border: none;
  font-size: 22px;
  color: #222;
  cursor: pointer;
  padding: 0 16px;
}
.top-title {
  font-size: 18px;
  font-weight: 700;
  color: #222;
  margin-left: 8px;
}
.tabs {
  display: flex;
  border-bottom: 1.5px solid #f2f2f2;
  margin-bottom: 18px;
  margin-top: 0;
}
.tab {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #bcbcbc;
  padding: 18px 0 12px 0;
  cursor: pointer;
  position: relative;
}
.tab.active {
  color: #111;
  border-bottom: 2.5px solid #111;
  background: #fff;
}
.section {
  margin: 0 18px 18px 18px;
  background: #fff;
}
.section-title {
  font-size: 19px;
  font-weight: 700;
  color: #111;
  margin-bottom: 12px;
  margin-top: 8px;
}
.steps {
  background: #fafafa;
  border-radius: 10px;
  padding: 16px 18px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.step {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #222;
  gap: 10px;
}
.icon {
  font-size: 18px;
}
.section-content {
  font-size: 16px;
  color: #444;
  line-height: 1.7;
}
.footer-link {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #888;
  font-size: 16px;
  margin-top: 32px;
  cursor: pointer;
  gap: 4px;
}
.arrow {
  font-size: 18px;
}
</style> 