<template>
  <div class="login-page">
    <!-- 装饰背景元素 -->
    <div class="decoration-circle circle-1"></div>
    <div class="decoration-circle circle-2"></div>
    <div class="decoration-circle circle-3"></div>

    <div class="login-container">
      <!-- 返回按钮 -->
      <router-link to="/" class="back-btn">
        <span class="back-arrow">←</span>
        {{ t('common.back') }}
      </router-link>

      <div class="form-container">
        <h1 class="title">{{ t('login.welcome') }}</h1>
        <p class="subtitle">{{ t('login.subtitle') }}</p>

        <form @submit.prevent="handleSubmit" class="login-form">
          <!-- 邮箱输入框 -->
          <div class="form-group" :class="{ 'has-error': emailError }">
            <label for="email">{{ t('login.email') }}</label>
            <div class="input-wrapper">
              <i class="input-icon">📧</i>
              <input
                type="email"
                id="email"
                v-model="email"
                @input="validateEmail"
                :placeholder="t('login.emailPlaceholder')"
                required
              />
            </div>
            <span class="error-message" v-if="emailError">{{ emailError }}</span>
          </div>

          <!-- 密码输入框 -->
          <div class="form-group" :class="{ 'has-error': passwordError }">
            <label for="password">{{ t('login.password') }}</label>
            <div class="input-wrapper">
              <i class="input-icon">🔒</i>
              <input
                :type="showPassword ? 'text' : 'password'"
                id="password"
                v-model="password"
                @input="validatePassword"
                :placeholder="t('login.passwordPlaceholder')"
                required
              />
              <button
                type="button"
                class="toggle-password"
                @click="showPassword = !showPassword"
              >
                {{ showPassword ? '👁️' : '👁️‍🗨️' }}
              </button>
            </div>
            <span class="error-message" v-if="passwordError">{{ passwordError }}</span>
          </div>



          <!-- 记住我和忘记密码 -->
          <div class="form-options">
            <label class="remember-me">
              <input type="checkbox" v-model="rememberMe" />
              <span>{{ t('login.rememberMe') }}</span>
            </label>
            <router-link to="/forgot-password" class="forgot-password">
              {{ t('login.forgotPassword') }}
            </router-link>
          </div>

          <!-- 登录按钮 -->
          <button
            type="submit"
            class="login-btn"
            :disabled="!isFormValid || isLoading"
          >
            <span v-if="!isLoading">{{ t('login.submit') }}</span>
            <span v-else class="loading-spinner"></span>
          </button>
          
          <!-- 登录错误信息 -->
          <div v-if="loginError" class="login-error">
            {{ loginError }}
          </div>
        </form>

        <!-- 分隔线 -->
        <div class="divider">
          <span>{{ t('login.or') }}</span>
        </div>
        <!-- 注册链接 -->
        <p class="register-link">
          {{ t('login.noAccount') }}
          <router-link to="/register">{{ t('login.register') }}</router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import axios from 'axios'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import userApi from '@/api/user'

const { t } = useI18n()

const router = useRouter()

// 表单数据
const email = ref('')
const password = ref('')
const showPassword = ref(false)
const rememberMe = ref(false)
const isLoading = ref(false)

// 错误信息
const emailError = ref('')
const passwordError = ref('')
const loginError = ref('') // 添加登录错误信息

// 表单验证
const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!email.value) {
    emailError.value = t('login.emailRequired')
  } else if (!emailRegex.test(email.value)) {
    emailError.value = t('login.emailInvalid')
  } else {
    emailError.value = ''
  }
}

const validatePassword = () => {
  if (!password.value) {
    passwordError.value = t('login.passwordRequired')
  } else if (password.value.length < 8) {
    passwordError.value = t('login.passwordLength')
  } else {
    passwordError.value = ''
  }
}



// 处理登录成功
const handleLoginSuccess = async (tokenResponse) => {
  const userStore = useUserStore()

  // 存储token信息
  const tokenData = {
    token: tokenResponse.access_token,
    refreshToken: tokenResponse.refresh_token,
    tokenType: tokenResponse.token_type || 'Bearer',
    expiresIn: tokenResponse.expires_in,
    scope: tokenResponse.scope,
    jti: tokenResponse.jti
  }

  localStorage.setItem('token', tokenResponse.access_token)
  localStorage.setItem('tokenData', JSON.stringify(tokenData))

  // 设置axios默认请求头携带token
  axios.defaults.headers.common['Authorization'] = `${tokenData.tokenType} ${tokenResponse.access_token}`

  // 如果选择了"记住我"，可以设置更长的过期时间
  if (rememberMe.value) {
    localStorage.setItem('rememberMe', 'true')
    userStore.setRememberMe(true)
  }

  // 获取用户信息
  try {
    const userInfoResponse = await userApi.getUserInfo()
    if (userInfoResponse.code === 0 && userInfoResponse.data) {
      // 存储用户数据
      const userData = userInfoResponse.data
      localStorage.setItem('userData', JSON.stringify(userData))

      // 更新Pinia store中的用户状态
      userStore.setUser(userData)

      console.log('用户登录成功，数据已保存', userData)
    }
  } catch (userInfoError) {
    console.error('获取用户信息失败:', userInfoError)
  }

  // 检查是否有重定向来源
  const redirectPath = localStorage.getItem('redirectFrom')

  // 登录成功后跳转到来源页面或首页
  if (redirectPath) {
    // 清除重定向信息
    localStorage.removeItem('redirectFrom')
    router.push(redirectPath)
  } else {
    // 没有重定向信息，跳转到首页
    router.push('/home')
  }
}

// 表单有效性
const isFormValid = computed(() => {
  return email.value && password.value && !emailError.value && !passwordError.value
})

// 表单提交
const handleSubmit = async () => {
  // 提交前进行最终验证
  validateEmail()
  validatePassword()
  if (!isFormValid.value) return
  try {
    isLoading.value = true
    loginError.value = '' // 清除之前的错误信息
    // 使用API进行登录
    const loginData = {
      email: email.value,
      password: password.value
    }

    // 使用邮箱登录接口
    const response = await userApi.login(loginData);
    console.log('登录响应:', response)

    // 检查登录响应
    if (response.access_token) {
      // OAuth2标准响应 - 登录成功
      await handleLoginSuccess(response)
    } else if (response.code === '0' || response.code === 0) {
      // 后端自定义成功响应格式 - 登录成功
      if (response.data && response.data.access_token) {
        await handleLoginSuccess(response.data)
      } else {
        // 没有token数据，可能需要进一步处理
        loginError.value = '登录响应格式异常，请联系管理员'
      }
    } else {
      // 登录失败
      loginError.value = response.msg || response.error_description || response.error || '登录失败，请检查账号和密码'
    }
  } catch (error) {
    console.error('Login failed:', error)
    // 处理错误响应
    if (error.response?.data) {
      const errorData = error.response.data;
      loginError.value = errorData.error_description || errorData.message || errorData.msg || '登录失败，请检查账号和密码'
    } else {
      loginError.value = error.message || '登录失败，请检查网络连接'
    }
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped lang="scss">
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 20px;
}

// 装饰圆圈样式（与Register.vue保持一致）
.decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  
  &.circle-1 {
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, #007bff, #00ff88);
    top: -100px;
    right: -100px;
    animation: float 8s ease-in-out infinite;
  }
  
  &.circle-2 {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, #ff3366, #ff9933);
    bottom: -50px;
    left: -50px;
    animation: float 6s ease-in-out infinite reverse;
  }
  
  &.circle-3 {
    width: 150px;
    height: 150px;
    background: linear-gradient(45deg, #6610f2, #6f42c1);
    top: 50%;
    right: -75px;
    animation: float 7s ease-in-out infinite;
  }
}

.login-container {
  width: 100%;
  max-width: 480px;
  position: relative;
  z-index: 1;
}

.back-btn {
  display: inline-flex;
  align-items: center;
  color: #666;
  text-decoration: none;
  font-size: 16px;
  margin-bottom: 24px;
  transition: color 0.3s ease;
  
  .back-arrow {
    margin-right: 8px;
    transition: transform 0.3s ease;
  }
  
  &:hover {
    color: #111;
    
    .back-arrow {
      transform: translateX(-4px);
    }
  }
}

.form-container {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.6s ease-out forwards;
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #111;
  margin-bottom: 8px;
}

.subtitle {
  color: #666;
  font-size: 16px;
  margin-bottom: 32px;
}

.form-group {
  margin-bottom: 24px;
  
  label {
    display: block;
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
  }
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  
  .input-icon {
    position: absolute;
    left: 16px;
    color: #666;
    font-style: normal;
  }
  
  input {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    
    &:focus {
      border-color: #111;
      outline: none;
      box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1);
    }
  }
  
  .toggle-password {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    font-size: 20px;
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .remember-me {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    
    input[type="checkbox"] {
      width: 16px;
      height: 16px;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  
  .forgot-password {
    color: #111;
    font-size: 14px;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.login-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(45deg, #111 0%, #333 100%);
  color: #fff;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

.login-error {
  color: #dc3545;
  font-size: 14px;
  margin-top: 12px;
  text-align: center;
  padding: 8px;
  background-color: rgba(220, 53, 69, 0.1);
  border-radius: 8px;
  border-left: 3px solid #dc3545;
}

.divider {
  text-align: center;
  margin: 24px 0;
  position: relative;
  
  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    width: calc(50% - 30px);
    height: 1px;
    background: #e0e0e0;
  }
  
  &::before { left: 0; }
  &::after { right: 0; }
  
  span {
    background: #fff;
    padding: 0 16px;
    color: #666;
    font-size: 14px;
  }
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e0e0e0;
    background: #fff;
    
    img {
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    &.google {
      color: #4285F4;
      border-color: #4285F4;
      background: rgba(66, 133, 244, 0.05);
    }
    
    &.apple {
      color: #000;
      border-color: #000;
      background: rgba(0, 0, 0, 0.05);
    }
  }
}

.register-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
  font-size: 14px;
  
  a {
    color: #111;
    font-weight: 600;
    text-decoration: none;
    transition: color 0.3s ease;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

// 表单验证状态样式
.form-group.has-error {
  .input-wrapper input {
    border-color: #dc3545;
  }
}

// 动画关键帧
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-container {
    padding: 30px 20px;
  }
  
  .title {
    font-size: 28px;
  }
  
  .subtitle {
    font-size: 14px;
    margin-bottom: 24px;
  }
  
  .login-btn {
    padding: 14px;
  }
  
  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>