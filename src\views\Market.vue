<template>
  <div class="market-page">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <svg width="20" height="20" viewBox="0 0 24 24"><circle cx="11" cy="11" r="7" stroke="#bbb" stroke-width="2" fill="none"/><line x1="18" y1="18" x2="22" y2="22" stroke="#bbb" stroke-width="2"/></svg>
      <input type="text" :placeholder="$t('market.searchPlaceholder')" />
    </div>
    <!-- 顶部tab -->
    <div class="main-tabs">
      <span class="active">{{ $t('market.tabs.market') }}</span>
      <span @click="$router.push('/dynamic')">{{ $t('market.tabs.dynamic') }}</span>
      <span @click="$router.push('/rank')">{{ $t('market.tabs.rank') }}</span>
    </div>
    <!-- 概览卡片 -->
    <div class="overview-cards">
      <div class="card">
        <div class="card-title">{{ $t('market.cards.totalValue') }}</div>
        <div class="card-value">$3.41万亿</div>
        <div class="card-change up">+1.51%</div>
      </div>
      <div class="card">
        <div class="card-title">{{ $t('market.cards.dailyVolume') }}</div>
        <div class="card-value">$658.37亿</div>
        <div class="card-change down">-34.89%</div>
      </div>
      <div class="card">
        <div class="card-title">{{ $t('market.cards.marketShare') }}</div>
        <div class="card-value"><svg width="18" height="18" viewBox="0 0 24 24" style="vertical-align:middle;margin-right:2px;"><circle cx="12" cy="12" r="10" fill="#f2a900"/></svg>61.51%</div>
        <div class="card-coin">{{ $t('market.cards.bitcoin') }}</div>
      </div>
    </div>
    <!-- 公告栏 -->
    <div class="notice-bar">
      <svg width="16" height="16" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" fill="#eee"/><rect x="11" y="7" width="2" height="6" fill="#888"/><rect x="11" y="15" width="2" height="2" fill="#888"/></svg>
      {{ $t('market.notice') }}
    </div>
    <!-- 筛选栏 -->
    <div class="filter-bar">
      <span class="active">{{ $t('market.filters[0]') }}</span>
      <span>{{ $t('market.filters[1]') }}</span>
      <span>{{ $t('market.filters[2]') }}</span>
      <span>{{ $t('market.filters[3]') }}</span>
      <span>{{ $t('market.filters[4]') }}</span>
    </div>
    <!-- 币种列表 -->
    <div v-if="mainTab==='market'" class="market-table">
      <div class="market-table-header">
        <div class="market-col asset-group">资产 / 当日最低</div>
        <div class="market-col rate" :class="{ active: sortField === 'rate' }" @click="handleSort('rate')">
          收益
          <svg v-if="sortField === 'rate'" width="12" height="12" viewBox="0 0 24 24" class="sort-icon">
            <path v-if="sortOrder === 'asc'" d="M7 14l5-5 5 5" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round"/>
            <path v-else d="M7 10l5 5 5-5" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round"/>
          </svg>
        </div>
        <div class="market-col hot" :class="{ active: sortField === 'hot' }" @click="handleSort('hot')">
          热度
          <svg v-if="sortField === 'hot'" width="12" height="12" viewBox="0 0 24 24" class="sort-icon">
            <path v-if="sortOrder === 'asc'" d="M7 14l5-5 5 5" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round"/>
            <path v-else d="M7 10l5 5 5-5" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round"/>
          </svg>
        </div>
        <div class="market-col wave" :class="{ active: sortField === 'wave' }" @click="handleSort('wave')">
          波动率
          <svg v-if="sortField === 'wave'" width="12" height="12" viewBox="0 0 24 24" class="sort-icon">
            <path v-if="sortOrder === 'asc'" d="M7 14l5-5 5 5" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round"/>
            <path v-else d="M7 10l5 5 5-5" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round"/>
          </svg>
        </div>
        <div class="market-col star"></div>
      </div>
      <div class="market-table-row" v-for="(item, idx) in sortedAssetList" :key="item.name + idx">
        <div class="market-col asset-group">
          <span class="icon" v-html="item.icon"></span>
          <div class="asset-info">
            <div class="asset-name">{{ item.name }}</div>
            <div class="asset-type">/ {{ item.type }}</div>
            <div class="asset-price">{{ item.price }}</div>
          </div>
        </div>
        <div class="market-col rate" :class="item.rateColor">{{ item.rate }}%</div>
        <div class="market-col hot">
          <span v-for="n in 3" :key="n" class="hot-flame">
            <svg v-if="item.hot >= n" width="18" height="18" viewBox="0 0 18 18" style="vertical-align:bottom;">
              <path d="M9 2C9 2 5 8 9 14C13 8 9 2 9 2Z" fill="#f66"/>
              <ellipse cx="9" cy="14" rx="4" ry="3" fill="#f66"/>
            </svg>
            <svg v-else width="18" height="18" viewBox="0 0 18 18" style="vertical-align:bottom;">
              <path d="M9 2C9 2 5 8 9 14C13 8 9 2 9 2Z" fill="#eee"/>
              <ellipse cx="9" cy="14" rx="4" ry="3" fill="#eee"/>
            </svg>
          </span>
        </div>
        <div class="market-col wave">
          <svg width="28" height="16" viewBox="0 0 28 16">
            <rect x="2" y="6" width="4" height="8" :fill="item.wave > 7 ? '#f66' : '#bbb'" rx="2"/>
            <rect x="12" y="4" width="4" height="10" :fill="item.wave > 6 ? '#fbb034' : '#bbb'" rx="2"/>
            <rect x="22" y="2" width="4" height="12" :fill="item.wave > 5 ? '#1bc47d' : '#bbb'" rx="2"/>
          </svg>
        </div>
        <div class="market-col star">
          <svg @click="item.star = !item.star" width="20" height="20" viewBox="0 0 24 24" style="cursor:pointer;transition:transform 0.18s;" :style="item.star ? 'transform:scale(1.2);' : ''">
            <polygon :points="'12,2 15,9 22,9 17,14 18.5,21 12,17.5 5.5,21 7,14 2,9 9,9'"
              :fill="item.star ? '#FFC107' : '#eee'"
              :stroke="item.star ? '#FFC107' : '#bbb'"
              stroke-width="1.5"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- 动态Tab -->
    <div v-if="mainTab==='news'" class="dynamic-tab-area">
      <div class="dynamic-sub-tabs">
        <span :class="{active: dynamicTab==='recommend'}" @click="dynamicTab='recommend'">{{ $t('market.dynamic.recommend') }}</span>
        <span :class="{active: dynamicTab==='newsflash'}" @click="dynamicTab='newsflash'">{{ $t('market.dynamic.newsflash') }}</span>
      </div>
      <div class="hot-news-area">
        <div class="hot-title">{{ $t('market.dynamic.hotNews') }}</div>
        <div class="hot-list">
          <div v-for="item in hotList" :key="item" class="hot-item">
            <span class="hot-hash">#</span>{{ item }}
          </div>
        </div>
      </div>
      <div class="dynamic-feed-list">
        <div class="feed-card" v-for="feed in feedList" :key="feed.id">
          <img :src="feed.avatar" class="feed-avatar" />
          <div class="feed-main">
            <div class="feed-header">
              <span class="feed-nick">{{ feed.nickname }}</span>
              <span class="feed-time">{{ feed.time }}</span>
            </div>
            <div class="feed-content">{{ feed.text }}</div>
            <div class="feed-links" v-if="feed.links">
              <span v-for="link in feed.links" :key="link" class="feed-link">{{ link }}</span>
            </div>
            <img v-if="feed.image" :src="feed.image" class="feed-img" />
          </div>
        </div>
      </div>
    </div>

    <!-- 牛人榜Tab -->
    <div v-if="mainTab==='rank'" class="rank-tab-area">
      <div class="rank-sub-tabs">
        <span :class="{active: rankTab==='rate'}" @click="rankTab='rate'">{{ $t('market.rank.rate') }}</span>
        <span :class="{active: rankTab==='amount'}" @click="rankTab='amount'">{{ $t('market.rank.amount') }}</span>
        <span :class="{active: rankTab==='asset'}" @click="rankTab='asset'">{{ $t('market.rank.asset') }}</span>
        <span class="rank-filter">{{ $t('market.rank.all') }}<svg width="14" height="14" viewBox="0 0 24 24" style="vertical-align:middle;"><path d="M7 10l5 5 5-5" stroke="#888" stroke-width="2" fill="none" stroke-linecap="round"/></svg></span>
      </div>
      <div class="rank-list">
        <div class="rank-item" v-for="item in rankList" :key="item.id">
          <div class="rank-avatar" :style="item.avatar ? '' : 'background:'+item.bgColor">
            <img v-if="item.avatar" :src="item.avatar" />
            <span v-else>{{ item.initial }}</span>
          </div>
          <div class="rank-info">
            <div class="rank-title">
              <span class="rank-name">{{ item.name }}</span>
              <span v-for="tag in item.tags" :key="tag.text" class="rank-tag" :class="tag.type">{{ tag.text }}</span>
            </div>
            <div class="rank-meta">
              <div class="rank-meta-col">
                <div class="rank-meta-label">{{ $t('market.rank.yield') }}</div>
                <div class="rank-meta-value rate">{{ item.rate }}</div>
              </div>
              <div class="rank-meta-col">
                <div class="rank-meta-label">{{ $t('market.rank.profit') }}</div>
                <div class="rank-meta-value amount">{{ item.amount }}</div>
              </div>
              <div class="rank-meta-col">
                <div class="rank-meta-label">{{ $t('market.rank.assets') }}</div>
                <div class="rank-meta-value asset">{{ item.asset }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <TabBar />
  </div>
</template>

<script setup>
import TabBar from '../components/TabBar.vue'
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const btcIcon = `<svg width="32" height="32" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="#F7931A"/><text x="16" y="22" text-anchor="middle" font-size="18" fill="#fff" font-family="Arial Black, Arial, sans-serif" font-weight="900">₿</text></svg>`
const ethIcon = `<svg width="32" height="32" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="#627EEA"/><polygon points="16,8 24,16 16,28 8,16" fill="#fff"/></svg>`
const chainlinkIcon = `<svg width="32" height="32" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="#375BD2"/><polygon points="16,8 24,16 16,24 8,16" fill="#fff"/></svg>`
const amazonIcon = `<svg width="32" height="32" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="#00A8E1"/><text x="16" y="22" text-anchor="middle" font-size="18" fill="#fff" font-family="Arial Black, Arial, sans-serif" font-weight="900">a</text></svg>`
const mcdonaldIcon = `<svg width="32" height="32" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="#FFC300"/><text x="16" y="22" text-anchor="middle" font-size="18" fill="#fff" font-family="Arial Black, Arial, sans-serif" font-weight="900">M</text></svg>`

const mainTab = ref('market')
const dynamicTab = ref('recommend')
const rankTab = ref('amount')

// 排序相关状态
const sortField = ref('')
const sortOrder = ref('desc')

const assetList = ref([
  { name: 'BTC', type: t('market.assetType.otc'), price: '93.1955 / 92.1966', rate: 85, rateColor: 'green', hot: 3, barColor: '#f66', star: true, icon: btcIcon, wave: 8.5 },
  { name: 'ETH', type: t('market.assetType.otc'), price: '2596.3015 / 2590.31', rate: 83, rateColor: 'green', hot: 2, barColor: '#f66', star: false, icon: ethIcon, wave: 6.2 },
  { name: 'Chainlink', type: t('market.assetType.otc'), price: '2596.3015 / 2590.31', rate: 80, rateColor: 'green', hot: 2, barColor: '#f66', star: false, icon: chainlinkIcon, wave: 7.8 },
  { name: 'Amazon', type: t('market.assetType.otc'), price: '93.1955 / 92.1966', rate: 85, rateColor: 'green', hot: 3, barColor: '#f66', star: true, icon: amazonIcon, wave: 9.1 },
  { name: 'McDonald', type: t('market.assetType.otc'), price: '93.1955 / 92.1966', rate: 85, rateColor: 'green', hot: 3, barColor: '#f66', star: false, icon: mcdonaldIcon, wave: 5.4 },
])

// 排序方法
const handleSort = (field) => {
  if (sortField.value === field) {
    // 如果点击的是当前排序字段，切换排序顺序
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    // 如果点击的是新字段，设置为降序
    sortField.value = field
    sortOrder.value = 'desc'
  }
}

// 计算属性：排序后的资产列表
const sortedAssetList = computed(() => {
  if (!sortField.value) {
    return assetList.value
  }
  
  return [...assetList.value].sort((a, b) => {
    let aValue, bValue
    
    switch (sortField.value) {
      case 'rate':
        aValue = a.rate
        bValue = b.rate
        break
      case 'hot':
        aValue = a.hot
        bValue = b.hot
        break
      case 'wave':
        aValue = a.wave
        bValue = b.wave
        break
      case 'star':
        aValue = a.star ? 1 : 0
        bValue = b.star ? 1 : 0
        break
      default:
        return 0
    }
    
    if (sortOrder.value === 'asc') {
      return aValue - bValue
    } else {
      return bValue - aValue
    }
  })
})

// 动态Tab假数据
const hotList = t('market.dynamic.hotTopics')
const feedList = [
  {
    id: 1,
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
    nickname: 'Ni',
    time: t('common.time.hoursAgo', { hours: 3 }),
    text: '并不是所有关于@satlayer 的事情都那么喧闹。\n真正的优势？在大家还在讨论的时候，悄悄地使用技术。',
    links: ['连接钱包', '接入社交', '点击 Discord', '更多'],
    image: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80'
  }
]
// 牛人榜Tab假数据
const rankList = [
  {
    id: 1,
    avatar: '',
    initial: '炒',
    bgColor: '#FFA54B',
    name: t('common.mockUsers.chaobi'),
    tags: [{ text: '空 2.96x', type: 'short' }],
    rate: '+429.34%',
    amount: '+$8,395,944',
    asset: '$2,484,975'
  },
  {
    id: 2,
    avatar: '',
    initial: 't',
    bgColor: '#C96B6B',
    name: 'tal***@proton.me',
    tags: [{ text: '多 0.05x', type: 'long' }],
    rate: '+10.42%',
    amount: '+$7,341,596',
    asset: '$39,378,087'
  },
  {
    id: 3,
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    initial: '',
    bgColor: '',
    name: t('common.mockUsers.kunpeng'),
    tags: [{ text: '多 0.11x', type: 'long' }, { text: '空 0.01x', type: 'short' }],
    rate: '+302.47%',
    amount: '+$5,772,342',
    asset: '$5,977,280'
  },
  {
    id: 4,
    avatar: '',
    initial: '金',
    bgColor: '#FFA54B',
    name: t('common.mockUsers.jinfa'),
    tags: [],
    rate: '+96.54%',
    amount: '+$5,758,694',
    asset: '$1,021,006'
  }
]
</script>

<style scoped lang="scss">
/* 添加涟漪效果的通用类 */
@mixin ripple-effect {
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
  }
  
  &:active::after {
    animation: ripple 0.6s ease-out;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

.market-page {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
  padding-bottom: 60px;
  
  > *:not(:first-child) {
    margin-top: 16px;
  }
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f6f6f6;
  border-radius: 8px;
  margin: 12px 12px 0 12px;
  padding: 6px 10px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
  transition: all 0.2s ease;
  
  &:focus-within {
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    background: #fff;
    border: 1px solid rgba(0,0,0,0.05);
  }
  
  svg { 
    margin-right: 5px; 
  }
  
  input {
    border: none;
    background: transparent;
    font-size: 14px;
    flex: 1;
    outline: none;
    padding: 4px 0;
    
    &::placeholder {
      color: #bbb;
    }
    
    &:focus::placeholder {
      color: #999;
    }
  }
}
.main-tabs {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 8px 0 0 0;
  border-bottom: 1px solid #eee;
  
  span {
    font-size: 15px;
    color: #888;
    padding: 8px 0;
    margin: 0 10px;
    cursor: pointer;
    position: relative;
    transition: color 0.2s ease;
    
    &:hover {
      color: #333;
    }
    
    &.active {
      color: #111;
      font-weight: bold;
      
      &::after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: -1px;
        transform: translateX(-50%);
        width: 24px;
        height: 2px;
        background: #111;
        border-radius: 1px;
      }
    }
  }
}
.overview-cards {
  display: flex;
  gap: 8px;
  margin: 10px 12px 0 12px;
  
  .card {
    flex: 1;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 8px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    box-shadow: 0 1px 4px rgba(0,0,0,0.02);
    transition: all 0.2s ease;
    
    &:hover {
      box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    }
    
    .card-title {
      font-size: 12px;
      color: #888;
      margin-bottom: 1px;
    }
    
    .card-value {
      font-size: 15px;
      font-weight: bold;
      margin-bottom: 1px;
      display: flex;
      align-items: center;
    }
    
    .card-change {
      font-size: 12px;
      
      &.up { 
        color: #1bc47d; 
        
        &::after {
          content: '↑';
          font-size: 9px;
          margin-left: 1px;
          position: relative;
          top: -1px;
        }
      }
      
      &.down { 
        color: #f66; 
        
        &::after {
          content: '↓';
          font-size: 9px;
          margin-left: 1px;
          position: relative;
          top: 1px;
        }
      }
    }
    
    .card-coin {
      font-size: 12px;
      color: #f2a900;
      font-weight: bold;
    }
  }
}
.notice-bar {
  background: #f6f6f6;
  margin: 8px 12px 0 12px;
  border-radius: 6px;
  padding: 5px 10px;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  
  &:hover {
    background: #f0f0f0;
  }
}

.filter-bar {
  display: flex;
  gap: 8px;
  margin: 8px 0 0 12px;
  font-size: 13px;
  overflow-x: auto;
  padding-bottom: 4px;
  
  &::-webkit-scrollbar {
    height: 2px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 2px;
  }
  
  span {
    color: #888;
    padding: 2px 8px;
    border-radius: 6px;
    cursor: pointer;
    white-space: nowrap;
    
    &:hover {
      color: #333;
    }
    
    &.active {
      background: #f6f6f6;
      color: #111;
      font-weight: bold;
      box-shadow: 0 1px 3px rgba(0,0,0,0.03);
    }
  }
}
.asset-list {
  margin: 10px 0 0 0;
  animation: fadeIn 0.5s ease-out 0.4s both;
  
  .asset-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f2f2f2;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 3px;
      background: #000;
      opacity: 0;
      transform: scaleY(0);
      transition: all 0.3s ease;
    }
    
    &:hover {
      background: rgba(0,0,0,0.01);
      padding-left: 20px;
      
      &::before {
        opacity: 0.1;
        transform: scaleY(0.7);
      }
      
      .asset-icon {
        transform: scale(1.05);
      }
      
      .asset-title .asset-name {
        color: #000;
      }
      
      .asset-star {
        transform: rotate(5deg) scale(1.1);
      }
    }
    
    &:active {
      background: rgba(0,0,0,0.03);
      transform: scale(0.995);
    }
    
    .asset-icon {
      width: 32px;
      height: 32px;
      margin-right: 10px;
      transition: all 0.3s ease;
      border-radius: 50%;
      overflow: hidden;
      box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    }
    
    .asset-info {
      flex: 1;
      
      .asset-title {
        font-size: 15px;
        font-weight: bold;
        
        .asset-name {
          transition: color 0.3s ease;
        }
        
        .asset-type {
          color: #888;
          font-size: 13px;
          font-weight: normal;
          transition: color 0.3s ease;
        }
      }
      
      .asset-price {
        color: #888;
        font-size: 13px;
        margin-top: 2px;
        transition: all 0.3s ease;
      }
    }
    
    .asset-extra {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      margin-right: 10px;
      
      .asset-rate {
        font-size: 14px;
        font-weight: bold;
        transition: all 0.3s ease;
        position: relative;
        
        &.green { 
          color: #1bc47d; 
          
          &::after {
            content: '↑';
            font-size: 10px;
            margin-left: 2px;
            position: relative;
            top: -1px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
          
          .asset-item:hover &::after {
            opacity: 1;
          }
        }
        
        &.red { 
          color: #f66; 
          
          &::after {
            content: '↓';
            font-size: 10px;
            margin-left: 2px;
            position: relative;
            top: 1px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
          
          .asset-item:hover &::after {
            opacity: 1;
          }
        }
      }
      
      .asset-hot {
        margin: 2px 0;
        transition: all 0.3s ease;
        
        .hot-dot {
          display: inline-block;
          width: 6px;
          height: 6px;
          background: #f66;
          border-radius: 50%;
          margin-right: 2px;
          transition: all 0.3s ease;
          
          .asset-item:hover & {
            transform: scale(1.2);
          }
        }
      }
      
      .asset-bar {
        margin-top: 2px;
        transition: all 0.3s ease;
        
        .asset-item:hover & {
          transform: scaleX(1.05);
        }
      }
    }
    
    .asset-star {
      margin-left: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &.active svg polygon {
        fill: #FFC107 !important;
      }
      
      &:hover {
        transform: rotate(10deg) scale(1.2);
      }
    }
  }
}
.icon-clock, .icon-dropdown {
  margin-left: 6px;
  fill: #bbb;
}
.dynamic-tab-area {
  padding: 0;
}

.dynamic-sub-tabs {
  display: flex;
  gap: 10px;
  margin: 12px 0 0 12px;
  font-size: 14px;
  
  span {
    color: #888;
    background: #f6f6f6;
    padding: 5px 16px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      color: #333;
      box-shadow: 0 1px 4px rgba(0,0,0,0.03);
    }
    
    &.active {
      color: #fff;
      font-weight: bold;
      background: #111;
      box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    }
  }
}

.hot-news-area {
  margin: 12px 0 0 0;
  padding: 0 0 0 12px;
  
  .hot-title {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 6px;
    position: relative;
    display: inline-block;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 20px;
      height: 2px;
      background: #111;
      border-radius: 1px;
    }
  }
  
  .hot-list {
    .hot-item {
      font-size: 13px;
      margin-bottom: 4px;
      padding: 3px 0;
      cursor: pointer;
      
      &:hover {
        background: rgba(0,0,0,0.02);
      }
      
      .hot-hash {
        color: #1bc47d;
        font-weight: bold;
        margin-right: 2px;
      }
    }
  }
}

.dynamic-feed-list {
  margin: 12px 0 0 0;
  
  .feed-card {
    display: flex;
    align-items: flex-start;
    padding: 12px;
    border-bottom: 1px solid #f2f2f2;
    cursor: pointer;
    
    &:hover {
      background: rgba(0,0,0,0.01);
    }
    
    .feed-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 8px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.03);
    }
    
    .feed-main {
      flex: 1;
      
      .feed-header {
        display: flex;
        align-items: center;
        gap: 6px;
        
        .feed-nick {
          font-weight: bold;
          font-size: 14px;
        }
        
        .feed-time {
          color: #888;
          font-size: 12px;
        }
      }
      
      .feed-content {
        font-size: 13px;
        margin: 4px 0 0 0;
        white-space: pre-line;
      }
      
      .feed-links {
        margin: 4px 0 0 0;
        
        .feed-link {
          color: #222;
          font-size: 12px;
          margin-right: 6px;
          text-decoration: underline;
          display: inline-block;
        }
      }
      
      .feed-img {
        width: 100%;
        max-width: 280px;
        border-radius: 8px;
        margin-top: 6px;
      }
    }
  }
}
.rank-tab-area {
  margin: 0;
}

.rank-sub-tabs {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 12px 0 0 0;
  padding: 0 12px;
  font-size: 14px;
  overflow-x: auto;
  
  &::-webkit-scrollbar {
    height: 2px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 2px;
  }
  
  span {
    color: #888;
    background: #f6f6f6;
    padding: 5px 16px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    
    &:hover {
      color: #333;
    }
    
    &.active {
      color: #fff;
      font-weight: bold;
      background: #111;
      box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    }
    
    &.rank-filter {
      margin-left: auto;
      color: #222;
      background: none;
      padding: 0;
      font-size: 13px;
      display: flex;
      align-items: center;
      
      svg {
        margin-left: 2px;
      }
    }
  }
}

.rank-list {
  margin: 12px 0 0 0;
  
  .rank-item {
    display: flex;
    align-items: flex-start;
    padding: 12px;
    border-bottom: 1px solid #f2f2f2;
    cursor: pointer;
    
    &:hover {
      background: rgba(0,0,0,0.01);
    }
    
    .rank-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: #eee;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: bold;
      color: #fff;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0,0,0,0.03);
      
      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }
    }
    
    .rank-info {
      flex: 1;
      
      .rank-title {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 6px;
        
        .rank-name {
          font-weight: bold;
          font-size: 14px;
        }
        
        .rank-tag {
          font-size: 12px;
          padding: 1px 6px;
          border-radius: 6px;
          
          &.long {
            background: #f8d7da;
            color: #d9534f;
          }
          
          &.short {
            background: #d4f8e8;
            color: #1bc47d;
          }
        }
      }
      
      .rank-meta {
        display: flex;
        gap: 16px;
        margin-top: 6px;
        
        .rank-meta-col {
          .rank-meta-label {
            color: #aaa;
            font-size: 12px;
          }
          
          .rank-meta-value {
            font-size: 14px;
            font-weight: bold;
            margin-top: 1px;
            
            &.rate, &.amount {
              color: #f66;
            }
            
            &.asset {
              color: #111;
            }
          }
        }
      }
    }
  }
}

.market-table {
  margin: 8px 0 0 0;
}
.market-table-row {
  display: flex;
  align-items: center;
  min-height: 68px;
  font-size: 13px;
  background: #fff;
}
.market-table-header {
  display: flex;
  align-items: center;
  min-height: 38px;
  font-size: 13px;
  background: #fff;
}

.market-col {
  padding: 0 10px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
  // 对齐方式
  &.asset-group { display: flex; align-items: center; min-width: 180px; }
  &.asset, &.asset-info { justify-content: flex-start; }
  &.price, &.rate, &.hot, &.wave, &.star { justify-content: flex-end; }
  &.asset { flex: 2; min-width: 100px; }
  &.price, &.rate, &.hot, &.wave { flex: 1; min-width: 60px; }
  &.star { flex: 0 0 24px; }
  
  /* 为收益率和热度列添加额外间距 */
  &.price {
    padding-left: 8px;
  }
  &.rate {
    padding-left: 16px;
    padding-right: 20px; /* 增加右侧padding */
  }
  
  &.hot {
    padding-left: 16px;
  }
}

.market-table-header {
  background: #fafbfc;
  color: #888;
  font-weight: 600;
  border-bottom: 1px solid #f2f2f2;
}

.market-table-row {
  border-bottom: 1px solid #f8f8f8;
  transition: background 0.2s;
  &:hover {
    background: #f5f7fa;
  }
}
.market-table-row:first-of-type {
  margin-top: 8px;
}
.asset-info {
  display: flex;
  flex-direction: column;
  margin-left: 6px;
  .asset-name { font-weight: bold; color: #222; font-size: 13px; }
  .asset-type { font-size: 11px; color: #bbb; }
  .asset-price { font-size: 11px; color: #888; }
}
.hot-dot {
  width: 6px; height: 6px; background: #f66; border-radius: 50%; margin-right: 2px;
  display: inline-block;
}
.rate.green { color: #1bc47d; font-weight: bold; }
.rate.red { color: #f66; font-weight: bold; }

/* 通用样式优化 */
* {
  box-sizing: border-box;
}

/* 响应式设计优化 */
@media screen and (min-width: 768px) {
  .market-page {
    max-width: 600px;
    margin: 0 auto;
  }
  
  .search-bar {
    margin: 16px 0 0 0;
  }
  
  .overview-cards {
    margin: 12px 0 0 0;
  }
  
  .notice-bar {
    margin: 10px 0 0 0;
  }
  
  .filter-bar {
    margin: 10px 0 0 0;
    justify-content: center;
  }
  
  .market-table-header, .market-table-row {
    padding: 0 8px;
  }
  
  .dynamic-sub-tabs, .rank-sub-tabs {
    justify-content: center;
    margin: 12px 0 0 0;
  }
  
  .hot-news-area {
    padding: 0;
    max-width: 90%;
    margin: 12px auto 0;
  }
}

@media screen and (min-width: 1024px) {
  .market-page {
    max-width: 800px;
  }
  
  .search-bar input {
    font-size: 15px;
  }
  
  .main-tabs span {
    font-size: 16px;
  }
  
  .card-value {
    font-size: 16px;
  }
  
  .market-table-header, .market-table-row {
    font-size: 14px;
  }
}

@media screen and (min-width: 1367px) {
  .market-page {
    max-width: 900px;
  }
}

.market-col.star {
  flex: 0 0 32px;
  justify-content: center;
  align-items: center;
  display: flex;
}
.market-col.star svg {
  width: 20px;
  height: 20px;
  display: block;
}
// 调整表头的收益和热度列左侧内边距
.market-table-header .market-col.rate {
  padding-left: 0;
  margin-left: -39px;
}
.market-table-header .market-col.hot {
  padding-left: 8px;
  margin-left: -10px;
}
// 移除数据行热度栏整体左移
.market-col.hot {
  margin-left: 0;
}
</style> 