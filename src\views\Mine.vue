<template>
  <div class="mine home page-enter">
    <!-- 顶部用户信息区 -->
    <div class="profile-bar new-profile-bar" @click="goToProfile">
      <img class="profile-avatar" :src="userAvatar" alt="avatar" />
      <div class="profile-info">
        <div class="profile-email">{{ maskedEmail }}</div>
        <div class="profile-id">ID:{{ user.sn }}</div>
        <div class="profile-tags">
          <span class="profile-auth" :class="{ 'unverified': !user.real_name }">{{ verificationStatus }}</span>
          <span class="profile-vip">{{ roleDisplay }}</span>
        </div>
      </div>
      <span class="profile-arrow">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#bbb" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 6l6 6-6 6"/></svg>
      </span>
      <span class="setting-icon" @click.stop="goToSetting">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#bbb" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>
      </span>
    </div>
    <div class="divider"></div>
    <!-- 常用功能区 -->
    <div class="section-title">{{ $t('mine.quickActions') }}</div>
    <div class="common-functions">
      <div class="function-item" v-for="btn in functionList" :key="btn.label" @click="handleBtnClick(btn)">
        <span class="function-icon" v-html="btn.svg"></span>
        <span class="function-label">{{ btn.label }}</span>
      </div>
    </div>
    <div class="divider"></div>
    <!-- 我的邀请 -->
    <div class="invite-section">
      <div class="invite-header">
        <span class="invite-title">{{ $t('invite.title', { amount: inviteList.length }) }}</span>
        <span class="invite-more" @click.stop="goToInvite">{{ $t('common.all') }} <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#bbb" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 6l6 6-6 6"/></svg></span>
      </div>
      <div class="invite-list" @scroll.passive="onInviteScroll">
        <div v-if="inviteList.length === 0 && inviteFinished && !inviteLoading" class="empty-state">
          <div class="empty-icon">🔍</div>
          <div class="empty-text">{{ $t('inviteList.noRecords') }}</div>
          <div class="empty-subtext">{{ $t('inviteList.emptySubtext') }}</div>
        </div>
        <div class="invite-item" v-for="item in inviteList" :key="item.id">
          <span class="invite-avatar-bg">
            <img class="invite-avatar" :src="item.avatarLink" alt="avatarLink" />
          </span>
          <span class="invite-name">{{ item.name || item.nickname || item.account || item.mobile }}</span>
          <span class="invite-reward">{{ item.reward || 0 }} USDT</span>
          <span class="invite-arrow">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#bbb" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 6l6 6-6 6"/></svg>
          </span>
        </div>
        <div v-if="inviteLoading" class="invite-loading">{{ $t('common.loading') }}</div>
        <div v-if="inviteError" class="invite-error">{{ $t('common.loadError') }}</div>
        <div v-if="inviteFinished && inviteList.length > 0 && !inviteLoading" class="invite-no-more">{{ $t('common.noMore') }}</div>
      </div>
    </div>
    <TabBar class="tab-bar" />
  </div>
</template>

<script setup>
import { computed, reactive, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { redirectToLogin } from '@/utils/auth'
import TabBar from '../components/TabBar.vue'
import userApi from '../api/user'
import defaultAvatar from '../assets/avatars/default.png'

const { t } = useI18n()

// SVG组件（严格贴合图片风格）
const HelpSvg = {
  template: `<svg width='32' height='32' viewBox='0 0 32 32' fill='none' stroke='#222' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><circle cx='16' cy='16' r='14'/><path d='M8 24v-2a4 4 0 0 1 4-4h8a4 4 0 0 1 4 4v2'/><rect x='13' y='13' width='6' height='4' rx='2'/><path d='M16 13v-3'/></svg>`
}
const MockTradeSvg = {
  template: `<svg width='32' height='32' viewBox='0 0 32 32' fill='none' stroke='#222' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><rect x='6' y='10' width='20' height='12' rx='3'/><rect x='13' y='14' width='6' height='4' rx='2'/><circle cx='12' cy='20' r='1'/><circle cx='20' cy='20' r='1'/></svg>`
}
const InviteSvg = {
  template: `<svg width='32' height='32' viewBox='0 0 32 32' fill='none' stroke='#222' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><rect x='6' y='14' width='20' height='10' rx='2'/><path d='M16 14V8'/><path d='M12 12l4-4 4 4'/></svg>`
}
const RewardSvg = {
  template: `<svg width='32' height='32' viewBox='0 0 32 32' fill='none' stroke='#222' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M16 6v8'/><path d='M16 18v2'/><circle cx='16' cy='24' r='2'/><path d='M8 10c2-4 14-4 16 0'/><path d='M8 22c2 4 14 4 16 0'/></svg>`
}
// 用户信息（由接口获取，初始为空）
const user = reactive({
  avatarLink: '',
  email: '',
  id: '',
  sn: '',
  role: '',
  account: '',
  mobile: '',
  nickname: '',
  create_time: '',
  real_name: '',
  age: '',
  levle: '',
  remark: '',
  sex: '',
  invitation_code: '',
})

// 缓存键名
const USER_CACHE_KEY = 'user_cache'

// 从缓存加载用户数据
function loadUserFromCache() {
  const cached = localStorage.getItem(USER_CACHE_KEY)
  if (cached) {
    try {
      const parsed = JSON.parse(cached)
      Object.assign(user, parsed)
      return true
    } catch (e) {
      console.error('解析缓存用户数据失败', e)
      localStorage.removeItem(USER_CACHE_KEY)
    }
  }
  return false
}

// 保存用户数据到缓存
function saveUserToCache() {
  localStorage.setItem(
    USER_CACHE_KEY, 
    JSON.stringify({
      avatarLink: user.avatarLink,
      id: user.id,
      sn: user.sn,
      role: user.role,
      account: user.account,
      mobile: user.mobile,
      nickname: user.nickname,
      create_time: user.create_time,
      real_name: user.real_name,
      age: user.age,
      levle: user.levle,
      remark: user.remark,
      sex: user.sex,
      invitation_code: user.invitation_code,
    })
  )
}

onMounted(async () => {
  // 1. 首先检查token是否存在
  const token = localStorage.getItem('token')
  if (!token) {
    redirectToLogin()
    return
  }

  // 2. 有token时尝试获取用户信息
  try {
    const res = await userApi.getUserInfo()
    // 获取用户数据
    user.avatarLink = res.data.avatarLink
    user.account = res.data.account
    user.id = res.data.id
    user.sn = res.data.sn
    user.sex = res.data.sex
    user.nickname = res.data.nickname
    user.create_time = res.data.create_time
    user.mobile = res.data.mobile
    user.remark = res.data.remark
    user.age = res.data.age
    user.real_name = res.data.real_name
    user.role = res.data.role
    user.levle = res.data.levle
    user.invitation_code = res.data.invitation_code
    console.log('获取用户信息成功', user)
    // 保存到缓存
    saveUserToCache()
  } catch (e) {
    console.error('获取用户信息失败', e)
    // 获取用户信息失败但token存在，不跳转登录页
    // 可以显示错误状态或尝试重新获取
  }
  fetchInviteList(true)
})
const maskedEmail = computed(() => {
  if (!user.account || typeof user.account !== 'string' || !user.account.includes('@')) return ''
  const [name, domain] = user.account.split('@')
  return name.slice(0, 3) + '***@' + domain
})

// 计算用户认证状态
const verificationStatus = computed(() => {
  return user.real_name ? t('profile.status.verified') : t('profile.status.unverified')
})

// 计算用户角色显示文本
const roleDisplay = computed(() => {
  if (user.role === '2') {
    // 经纪人，拼接level字段，格式为"经纪人V1"
    return `${t('mine.broker')}V${user.levle}`
  } else {
    // 普通用户
    return t('mine.user')
  }
})
// 常用功能按钮
const functionList = [
  { svg: `<svg width='32' height='32' viewBox='0 0 32 32' fill='none' stroke='#222' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><circle cx='16' cy='16' r='14'/><path d='M8 24v-2a4 4 0 0 1 4-4h8a4 4 0 0 1 4 4v2'/><rect x='13' y='13' width='6' height='4' rx='2'/><path d='M16 13v-3'/></svg>`, label: t('mine.help'), to: '/help' },
  { svg: `<svg width='32' height='32' viewBox='0 0 32 32' fill='none' stroke='#222' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><rect x='6' y='10' width='20' height='12' rx='3'/><rect x='13' y='14' width='6' height='4' rx='2'/><circle cx='12' cy='20' r='1'/><circle cx='20' cy='20' r='1'/></svg>`, label: t('mine.mockTrade'), to: '/mock-trade' },
  { svg: `<svg width='32' height='32' viewBox='0 0 32 32' fill='none' stroke='#222' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><rect x='6' y='14' width='20' height='10' rx='2'/><path d='M16 14V8'/><path d='M12 12l4-4 4 4'/></svg>`, label: t('mine.invite'), to: '/invite' },
  { svg: `<svg width='32' height='32' viewBox='0 0 32 32' fill='none' stroke='#222' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><path d='M16 6v8'/><path d='M16 18v2'/><circle cx='16' cy='24' r='2'/><path d='M8 10c2-4 14-4 16 0'/><path d='M8 22c2 4 14 4 16 0'/></svg>`, label: t('mine.reward'), to: '/reward' },
]
function handleBtnClick(btn) {
  if (btn.to) window.location.href = btn.to
}
const router = useRouter()
// 检查登录状态
function isLoggedIn() {
  // 1. 首先检查token是否存在
  const token = localStorage.getItem('token')
  if (token) {
    return true // token存在即认为已登录
  }
  
  // 2. 没有token时检查用户信息
  return !!user.id || !!user.account
}

function goToProfile() {
  if (!isLoggedIn()) {
    alert('请先登录')
    setTimeout(redirectToLogin, 500)
    return
  }
  router.push('/profile')
}
function goToInvite() {
  router.push('/invite-list')
}

function goToSetting() {
  if (!isLoggedIn()) {
    alert('请先登录')
    setTimeout(redirectToLogin, 500)
    return
  }
  router.push('/setting')
}
// 我的邀请数据与分页
const inviteList = ref([])
const invitePage = ref(1)
const invitePageSize = 10
const inviteLoading = ref(false)
const inviteFinished = ref(false)
const inviteError = ref(false)

// 获取邀请人列表
async function fetchInviteList(reset = false) {
  if (inviteLoading.value || inviteFinished.value) return
  inviteLoading.value = true
  inviteError.value = false
  try {
    const res = await userApi.listByBelong({ page: invitePage.value, pageSize: invitePageSize, belong: user.id })
    const list = res.data?.lists || []
    if (reset) inviteList.value = []
    if (list.length > 0) {
      inviteList.value = reset ? list : inviteList.value.concat(list)
      if (list.length < invitePageSize) inviteFinished.value = true
      else invitePage.value++
    } else {
      if (invitePage.value === 1) inviteList.value = []
      inviteFinished.value = true
    }
  } catch (e) {
    inviteError.value = true
    console.error('获取邀请人失败', e)
  } finally {
    inviteLoading.value = false
  }
}

// 滚动加载
function onInviteScroll(e) {
  const el = e.target
  if (el.scrollHeight - el.scrollTop - el.clientHeight < 50) {
    fetchInviteList()
  }
}

// 用户头像优先级：用户头像 > 实名SVG > 本地默认头像
const userAvatar = computed(() => {
  if (user.avatarLink && user.avatarLink.trim() !== '') {
    return user.avatarLink
  }
  if (user.real_name && user.real_name.length > 0) {
    const initial = user.real_name[0].toUpperCase()
    const svg = `<svg width='52' height='52' xmlns='http://www.w3.org/2000/svg'><rect width='52' height='52' fill='#eee'/><text x='50%' y='50%' text-anchor='middle' dy='.35em' font-size='28' fill='#888'>${initial}</text></svg>`
    return `data:image/svg+xml;base64,${btoa(svg)}`
  }
  return defaultAvatar
})
</script>

<style scoped lang="scss">
.mine.home {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
  padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px));
  position: relative;
  overflow: hidden;
}
.profile-bar {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 0;
  padding: 24px 0 20px 18px;
  margin: 0 auto 0 auto;
  max-width: 420px;
  width: 100%;
  position: relative;
  min-height: 88px;
  border-bottom: none;
  cursor: pointer;
}
.profile-avatar {
  width: 52px;
  height: 52px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 12px;
  border: none;
}
.profile-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  justify-content: center;
}
.profile-email {
  font-size: 16px;
  font-weight: 700;
  color: #222;
  margin-bottom: 2px;
}
.profile-id {
  font-size: 12px;
  color: #bbb;
  margin-bottom: 2px;
}
.profile-tags {
  display: flex;
  gap: 6px;
  margin-top: 2px;
}
.profile-auth {
  font-size: 13px;
  color: #1bc47d;
  background: #fff;
  border: 1px solid #1bc47d;
  border-radius: 8px;
  padding: 1px 8px 1px 8px;
  font-weight: 500;
  display: inline-block;
  line-height: 18px;
  
  &.unverified {
    color: #ff9800;
    border-color: #ff9800;
  }
}
.profile-vip {
  font-size: 13px;
  color: #8e44ad;
  background: #fff;
  border: 1px solid #8e44ad;
  border-radius: 8px;
  padding: 1px 8px 1px 8px;
  font-weight: 500;
  display: inline-block;
  line-height: 18px;
}
.profile-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  margin-right: 12px;
}
.setting-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  cursor: pointer;
}
.section-title {
  font-size: 16px;
  font-weight: 700;
  color: #222;
  margin: 24px 0 0 18px;
  letter-spacing: 0.2px;
  text-align: left;
}
.common-functions {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: 0 0 0 0;
  padding: 0 0 0 0;
  background: #fff;
  border-bottom: 1px solid #eee;
}
.function-item {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #222;
  background: none;
  box-shadow: none;
  cursor: pointer;
  min-width: 0;
  padding: 12px 0 10px 0;
}
.function-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
}
.function-label {
  font-size: 13px;
  margin-top: 0;
  font-weight: 400;
  color: #222;
  letter-spacing: 0.2px;
  text-align: center;
}
.divider {
  height: 1px;
  background: #eee;
  width: 100vw;
  margin-left: 50%;
  transform: translateX(-50%);
}
.invite-section {
  background: #fff;
  border-radius: 0;
  margin: 0 auto 0 auto;
  max-width: 420px;
  width: 100%;
  padding: 0;
}
.invite-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 18px 12px 18px;
  margin-top: 0;
}
.invite-title {
  font-size: 15px;
  font-weight: 600;
  color: #222;
}
.invite-more {
  font-size: 13px;
  color: #888;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 2px;
}
.invite-list {
  display: flex;
  flex-direction: column;
  gap: 0;
  max-height: 460px;
  overflow-y: auto;
}
.invite-empty {
  text-align: center;
  color: #bbb;
  padding: 32px 0;
  font-size: 15px;
}
.invite-loading {
  text-align: center;
  color: #888;
  padding: 12px 0;
  font-size: 14px;
}
.invite-error {
  text-align: center;
  color: #ff9800;
  padding: 12px 0;
  font-size: 14px;
}
.invite-no-more {
  text-align: center;
  color: #bbb;
  padding: 12px 0;
  font-size: 14px;
}
.invite-item {
  display: flex;
  align-items: center;
  padding: 14px 18px;
  border-bottom: 1px solid #f5f5f5;
  background: #fff;
  transition: background-color 0.2s ease;
}

.invite-item:hover {
  background: #fafafa;
}
.invite-avatar-bg {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e8f5e8 0%, #d4f0d4 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
  box-shadow: 0 2px 8px rgba(27, 196, 125, 0.1);
}
.invite-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
}
.invite-name {
  flex: 1;
  font-size: 15px;
  font-weight: 500;
  color: #222;
  margin-right: 8px;
}
.invite-reward {
  font-size: 14px;
  font-weight: 600;
  color: #1bc47d;
  margin-right: 12px;
  min-width: 75px;
  text-align: right;
  background: rgba(27, 196, 125, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
}
.invite-arrow {
  font-size: 16px;
  color: #ccc;
  display: flex;
  align-items: center;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.invite-item:hover .invite-arrow {
  opacity: 1;
}
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  padding-bottom: env(safe-area-inset-bottom, 0px);
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
</style> 