<template>
  <div class="mock-trade-page">
    <div class="top-bar">
      <span class="icon-grid">
        <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#222" stroke-width="2"><circle cx="5" cy="5" r="2"/><circle cx="12" cy="5" r="2"/><circle cx="19" cy="5" r="2"/><circle cx="5" cy="12" r="2"/><circle cx="12" cy="12" r="2"/><circle cx="19" cy="12" r="2"/><circle cx="5" cy="19" r="2"/><circle cx="12" cy="19" r="2"/><circle cx="19" cy="19" r="2"/></svg>
      </span>
      <span class="top-title">{{ $t('mockTrade.title') }}</span>
      <button class="exit-btn">{{ $t('mockTrade.exit') }}</button>
    </div>
    <div class="asset-block">
      <span class="asset-label">{{ $t('mockTrade.totalAssetValue') }}</span>
      <svg class="eye" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#888" stroke-width="2"><circle cx="12" cy="12" r="10"/><circle cx="12" cy="12" r="4"/></svg>
      <div class="asset-value">0.00 <span class="asset-unit">USD <svg width="12" height="12" viewBox="0 0 24 24"><path d="M7 10l5 5 5-5" stroke="#222" stroke-width="2" fill="none"/></svg></span></div>
    </div>
    <div class="main-section">
      <div class="section-title">{{ $t('mockTrade.mainCoins') }}</div>
      <div class="search-bar">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#bbb" stroke-width="2"><circle cx="11" cy="11" r="8"/><line x1="21" y1="21" x2="16.65" y2="16.65"/></svg>
        <input class="search-input" :placeholder="$t('mockTrade.search')" />
      </div>
      <div class="coin-table">
        <div class="coin-table-header">
          <span class="coin-name">{{ $t('mockTrade.name') }}</span>
          <span class="coin-price">{{ $t('mockTrade.latestPrice') }}</span>
          <span class="coin-change">{{ $t('mockTrade.todayChange') }}</span>
        </div>
        <div class="coin-row" v-for="coin in coins" :key="coin.symbol">
          <span class="coin-icon-wrap" v-html="coin.svg"></span>
          <span class="coin-info">
            <span class="coin-symbol">{{ coin.symbol }}</span>
            <span class="coin-fullname">{{ coin.name }}</span>
          </span>
          <span class="coin-price">${{ coin.price }}</span>
          <span class="coin-change" :class="coin.change > 0 ? 'up' : 'down'">
            {{ coin.change > 0 ? '+' : '' }}{{ coin.change }}%
          </span>
        </div>
      </div>
    </div>
    <TabBar />
  </div>
</template>

<script setup>
import TabBar from '../components/TabBar.vue'
const coins = [
  { symbol: 'BTC', name: 'Bitcoin', price: '109,654.1', change: 0.44, svg: `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><circle cx='16' cy='16' r='16' fill='#F7931A'/><path d='M23.189 14.02c.314-2.096-1.283-3.223-3.465-3.975l.708-2.84-1.728-.43-.69 2.765c-.454-.114-.92-.22-1.385-.326l.695-2.783L15.596 6l-.708 2.839c-.376-.086-.746-.17-1.104-.26l.002-.009-2.384-.595-.46 1.846s1.283.294 1.256.312c.7.175.826.638.805 1.006l-.806 3.235c.*************.18.057l-.183-.045-1.13 4.532c-.086.212-.303.531-.793.41.018.025-1.256-.313-1.256-.313l-.858 1.978 2.25.561c.418.105.828.215 1.231.318l-.715 2.872 1.727.43.708-2.84c.472.127.93.245 1.378.357l-.706 2.828 1.728.43.715-2.866c2.948.558 5.164.333 6.097-2.333.752-2.146-.037-3.385-1.588-4.192 1.13-.26 1.98-1.003 2.207-2.538zm-3.95 5.538c-.533 2.147-4.148.986-5.32.695l.95-3.805c1.172.293 4.929.872 4.37 3.11zm.535-5.569c-.487 1.953-3.495.96-4.47.717l.86-3.45c.975.243 4.118.696 3.61 2.733z' fill='#FFF'/></svg>` },
  { symbol: 'ETH', name: 'Ethereum', price: '2,598.1', change: 1.05, svg: `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><circle cx='16' cy='16' r='16' fill='#627EEA'/><path d='M16.498 4v8.87l7.497 3.35L16.498 4z' fill='#FFF' fill-opacity='.602'/><path d='M16.498 4L9 16.22l7.498-3.35V4z' fill='#FFF'/><path d='M16.498 21.968v6.027L24 17.616l-7.502 4.352z' fill='#FFF' fill-opacity='.602'/><path d='M16.498 27.995v-6.028L9 17.616l7.498 10.379z' fill='#FFF'/><path d='M16.498 20.573l7.497-4.353-7.497-3.348v7.701z' fill='#FFF' fill-opacity='.2'/><path d='M9 16.22l7.498 4.353v-7.701L9 16.22z' fill='#FFF' fill-opacity='.602'/></svg>` },
  { symbol: 'SOL', name: 'Solana', price: '153.062', change: 1.88, svg: `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 48 48'><path fill='#2196F3' d='M42 32v4H6V12h4v20z'/><path fill='#2196F3' d='M42 6H6v4h36z'/><path fill='#2196F3' d='M12 18h4v12h-4zm8 5h4v7h-4zm8-3h4v10h-4zm8-4h4v14h-4z'/></svg>` },
  { symbol: 'DOGE', name: 'Dogecoin', price: '0.1726119', change: 1.74, svg: `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><circle cx='16' cy='16' r='16' fill='#F3BA2F'/><path fill='#FFF' d='M12.116 14.404L16 10.52l3.886 3.886 2.26-2.26L16 6l-6.144 6.144 2.26 2.26zM6 16l2.26-2.26L10.52 16l-2.26 2.26L6 16zm6.116 1.596L16 21.48l3.886-3.886 2.26 2.259L16 26l-6.144-6.144-.003-.003 2.263-2.257zM21.48 16l2.26-2.26L26 16l-2.26 2.26L21.48 16zm-3.188-.002h.002V16L16 18.294l-2.291-2.29-.004-.004.004-.003.401-.402.195-.195L16 13.706l2.293 2.293z'/></svg>` },
  { symbol: 'XRP', name: 'XRP', price: '2.26581', change: 0.97, svg: `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64'><circle cx='32' cy='32' r='30' fill='#4CAF50'/><path d='M27 42L16 31l-3 3 14 14 24-24-3-3z' fill='#FFF'/></svg>` },
  { symbol: 'BCH', name: 'Bitcoin Cash', price: '495.06', change: 0.43, svg: `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M16.498 8c.825 0 1.5.675 1.5 1.5s-.675 1.5-1.5 1.5-1.5-.675-1.5-1.5.675-1.5 1.5-1.5zm0-2c-1.933 0-3.5 1.567-3.5 3.5s1.567 3.5 3.5 3.5 3.5-1.567 3.5-3.5-1.567-3.5-3.5-3.5z' fill='#000'/><path d='M22.498 13h-12c-1.104 0-2 .896-2 2v6c0 1.104.896 2 2 2h12c1.104 0 2-.896 2-2v-6c0-1.104-.896-2-2-2zm0 8h-12v-6h12v6z' fill='#000'/><path d='M16.498 15c-1.104 0-2 .896-2 2s.896 2 2 2 2-.896 2-2-.896-2-2-2z' fill='#000'/></svg>` },
  { symbol: 'LTC', name: 'Litecoin', price: '88.54', change: -0.49, svg: `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 48 48'><path d='M32.377 25.116c-.017-1.984.865-3.474 2.652-4.579-1.007-1.465-2.526-2.277-4.541-2.438-1.906-.156-3.989 1.134-4.753 1.134-.808 0-2.672-1.073-4.14-1.073-3.23.052-6.348 2.647-6.348 7.969 0 1.573.277 3.192.831 4.856 1.073 3.101 3.745 8.015 6.771 8.015 1.569-.021 2.696-1.126 4.745-1.126 1.992 0 3.023 1.126 4.745 1.126 3.043 0 5.527-4.627 6.549-7.733-4.15-1.986-4.511-5.83-4.511-6.151zm-4.269-11.116c1.512-1.839 1.378-3.518 1.333-4h-.113c-1.134.091-2.44.771-3.207 1.666-.842.961-1.471 2.206-1.283 3.534.***************.269.035 1.001 0 2.204-.648 3.001-1.235z'/></svg>` },
]
</script>

<style scoped lang="scss">
.mock-trade-page {
  background: #fff;
  min-height: 100vh;
  padding-bottom: 60px;
}
.top-bar {
  display: flex;
  align-items: center;
  padding: 16px 20px 0 20px;
  background: #fff;
  gap: 10px;
}
.icon-grid {
  display: flex;
  align-items: center;
}
.top-title {
  font-size: 18px;
  font-weight: 700;
  margin-left: 8px;
  flex: 1;
}
.exit-btn {
  background: #111;
  color: #fff;
  border: none;
  border-radius: 22px;
  padding: 6px 22px;
  font-size: 15px;
  font-weight: 600;
  margin-left: auto;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: background 0.2s;
  &:hover {
    background: #333;
  }
}
.asset-block {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 18px 0 0 20px;
}
.asset-label {
  color: #888;
  font-size: 14px;
}
.asset-value {
  font-size: 28px;
  font-weight: 800;
  color: #111;
  margin-left: 0;
  display: flex;
  align-items: baseline;
}
.asset-unit {
  font-size: 14px;
  color: #222;
  margin-left: 6px;
  display: inline-flex;
  align-items: center;
}
.eye {
  margin-left: 4px;
}
.main-section {
  margin: 20px 0 0 0;
  padding: 0;
}
.section-title {
  font-size: 16px;
  font-weight: 700;
  color: #111;
  margin-left: 20px;
  margin-bottom: 8px;
}
.search-bar {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 18px;
  padding: 5px 16px;
  margin: 0 20px 14px 20px;
}
.search-input {
  border: none;
  background: transparent;
  font-size: 15px;
  margin-left: 8px;
  outline: none;
  width: 100%;
}
.coin-table {
  margin: 0;
}
.coin-table-header {
  display: flex;
  align-items: center;
  color: #aaa;
  font-size: 14px;
  font-weight: 500;
  padding: 0 20px 6px 20px;
}
.coin-name {
  flex: 2;
}
.coin-price {
  flex: 2;
  text-align: left;
}
.coin-change {
  flex: 1.2;
  text-align: right;
}
.coin-row {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #f2f2f2;
  transition: background 0.2s;
  cursor: pointer;
  &:hover {
    background: #f7f7f7;
  }
}
.coin-icon-wrap {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7f7f7;
  border-radius: 50%;
  margin-right: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  border: 1px solid #f0f0f0;
}
.coin-icon-img {
  width: 28px;
  height: 28px;
  object-fit: contain;
  display: block;
  background: #fff;
  border-radius: 50%;
}
.coin-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.coin-symbol {
  font-size: 15px;
  font-weight: 700;
  color: #111;
}
.coin-fullname {
  font-size: 12px;
  color: #888;
}
.coin-price {
  flex: 2;
  font-size: 15px;
  font-weight: 600;
  color: #111;
}
.coin-change {
  flex: 1.2;
  font-size: 14px;
  font-weight: 600;
  text-align: right;
  border-radius: 8px;
  padding: 3px 10px;
  display: inline-block;
}
.coin-change.up {
  background: #2ecc71;
  color: #fff;
}
.coin-change.down {
  background: #ff4d4f;
  color: #fff;
}
@media (max-width: 600px) {
  .mock-trade-page {
    padding-bottom: 56px;
  }
  .top-bar, .asset-block, .main-section, .section-title, .search-bar, .coin-table-header, .coin-row {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
  .asset-value {
    font-size: 22px;
  }
  .section-title {
    font-size: 15px;
  }
  .coin-symbol {
    font-size: 13px;
  }
  .coin-fullname {
    font-size: 11px;
  }
  .coin-price {
    font-size: 13px;
  }
  .coin-change {
    font-size: 12px;
    padding: 2px 6px;
  }
}
</style> 