<template>
  <div class="payment-page">
    <!-- 顶部栏 -->
    <div class="payment-header">
      <button class="icon-btn" @click="$router.back()">
        <svg width="20" height="20" viewBox="0 0 24 24"><path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/></svg>
      </button>
      <span class="title">{{ t('payment.title') }}</span>
    </div>

    <!-- 充值金额输入 -->
    <div class="amount-section">
      <div class="amount-label">{{ t('payment.amount') }}</div>
      <div class="amount-input-container">
        <span class="currency-symbol">$</span>
        <input 
          type="text" 
          v-model="amount" 
          class="amount-input" 
          :placeholder="$t('payment.amountPlaceholder')"
          @input="validateAmount"
        />
      </div>
      
      <!-- 快捷金额选择 -->
      <div class="quick-amounts">
        <button 
          v-for="quickAmount in quickAmounts" 
          :key="quickAmount"
          :class="['quick-amount-btn', { active: amount === quickAmount.toString() }]"
          @click="amount = quickAmount.toString()"
        >
          ${{ quickAmount }}
        </button>
      </div>
    </div>

    <!-- 支付方式标题 -->
    <div class="section-title">{{ t('payment.selectPaymentMethod') }}</div>
    
    <!-- 支付方式列表 -->
    <div class="payment-list">
      <div
        v-for="method in paymentMethods"
        :key="method.id"
        class="payment-item"
        :class="{ active: selectedId === method.id }"
        @click="selectedId = method.id"
      >
        <div class="payment-icon" v-html="method.icon"></div>
        <div class="payment-info">
          <span class="payment-name">{{ method.name }}</span>
          <span class="payment-card" v-if="method.card">{{ method.card }}</span>
        </div>
        <div class="payment-check">
          <svg v-if="selectedId === method.id" width="24" height="24" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="12" fill="#1bc47d"/>
            <path d="M8 12l3 3 5-5" stroke="#fff" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- 确认按钮 -->
    <button 
      class="confirm-btn" 
      :disabled="isButtonDisabled"
      @click="confirmPayment"
    >
      {{ isProcessing ? $t('payment.processing') : $t('payment.confirmPayment') }}
    </button>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()
const amount = ref('')
const quickAmounts = [50, 100, 200, 500, 1000]
const isProcessing = ref(false)

const paymentMethods = [
  { id: 'paypal', name: 'PayPal', icon: `<svg width="28" height="28" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="#2196F3"/><text x="16" y="22" text-anchor="middle" font-size="16" fill="#fff" font-family="Arial Black, Arial, sans-serif" font-weight="900">P</text></svg>` },
  { id: 'google', name: 'Google Pay', icon: `<svg width="28" height="28" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="#fff"/><text x="16" y="22" text-anchor="middle" font-size="16" fill="#4285F4" font-family="Arial Black, Arial, sans-serif" font-weight="900">G</text></svg>` },
  { id: 'apple', name: 'Apple Pay', icon: `<svg width="28" height="28" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="#111"/><text x="16" y="22" text-anchor="middle" font-size="16" fill="#fff" font-family="Arial Black, Arial, sans-serif" font-weight="900"></text></svg>` },
  { id: 'master', name: '', icon: `<svg width="28" height="28" viewBox="0 0 32 32"><circle cx="12" cy="16" r="10" fill="#F44336"/><circle cx="20" cy="16" r="10" fill="#FFA000"/></svg>`, card: '**** **** **** 5567' },
  { id: 'btc', name: 'BTC', icon: `<svg width="28" height="28" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="#F7931A"/><text x="16" y="22" text-anchor="middle" font-size="16" fill="#fff" font-family="Arial Black, Arial, sans-serif" font-weight="900">₿</text></svg>` }
]
const selectedId = ref('master')

// 验证金额输入
const validateAmount = () => {
  // 只允许数字和小数点
  amount.value = amount.value.replace(/[^\d.]/g, '')
  
  // 确保只有一个小数点
  const parts = amount.value.split('.')
  if (parts.length > 2) {
    amount.value = parts[0] + '.' + parts.slice(1).join('')
  }
  
  // 限制小数点后两位
  if (parts.length > 1 && parts[1].length > 2) {
    amount.value = parts[0] + '.' + parts[1].substring(0, 2)
  }
}

// 计算按钮是否可用
const isButtonDisabled = computed(() => {
  return !amount.value || parseFloat(amount.value) <= 0 || isProcessing.value
})

// 确认支付
const confirmPayment = () => {
  if (isButtonDisabled.value) return
  
  isProcessing.value = true
  
  // 获取选中的支付方式
  const selectedMethod = paymentMethods.find(method => method.id === selectedId.value)
  
  // 显示确认信息
  console.log(t('common.console.paymentConfirm', { 
    amount: amount.value, 
    method: selectedMethod.name || t('payment.creditCard') 
  }))
  
  // 模拟支付处理
  setTimeout(() => {
    alert(t('common.alerts.paymentSuccess'))
    isProcessing.value = false
    router.push('/asset')
  }, 1500)
}
</script>

<style scoped lang="scss">
.payment-page {
  min-height: 100vh;
  background: #f8f8f8;
}

.payment-header {
  display: grid;
  grid-template-columns: 40px 1fr 40px;
  align-items: center;
  padding: calc(env(safe-area-inset-top) + 12px) 0 18px;
  background: #fff;
  border-bottom: 1px solid #eee;
  position: relative;
  .icon-btn {
    grid-column: 1;
    justify-self: start;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: #222;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s;
    &:active {
      background: #f5f5f5;
      transform: scale(0.95);
    }
  }
  .title {
    grid-column: 2;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    pointer-events: none;
  }
}

.amount-section {
  padding: 24px 16px;
  background: #fff;
  
  .amount-label {
    font-size: 16px;
    color: #666;
    margin-bottom: 12px;
  }
  
  .amount-input-container {
    display: flex;
    align-items: center;
    background: #f8f8f8;
    border-radius: 12px;
    padding: 12px 16px;
    margin-bottom: 16px;
    
    .currency-symbol {
      font-size: 24px;
      color: #333;
      margin-right: 8px;
      font-weight: bold;
    }
    
    .amount-input {
      flex: 1;
      border: none;
      background: transparent;
      font-size: 24px;
      color: #333;
      outline: none;
      font-weight: bold;
      
      &::placeholder {
        color: #999;
      }
    }
  }
  
  .quick-amounts {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 16px;
    
    .quick-amount-btn {
      flex: 1;
      min-width: 80px;
      padding: 10px;
      border: 1px solid #eee;
      border-radius: 8px;
      background: #fff;
      color: #333;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        border-color: #1bc47d;
        color: #1bc47d;
      }
      
      &.active {
        background: #1bc47d;
        color: #fff;
        border-color: #1bc47d;
      }
    }
  }
}

.section-title {
  padding: 16px;
  font-size: 16px;
  color: #666;
  background: #f8f8f8;
}

.payment-list {
  background: #fff;
  padding: 0 16px;
  
  .payment-item {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    
    &:last-child {
      border-bottom: none;
    }
    
    &.active {
      background: #f8f8f8;
    }
    
    .payment-icon {
      margin-right: 16px;
    }
    
    .payment-info {
      flex: 1;
      
      .payment-name {
        font-size: 16px;
        color: #333;
        margin-bottom: 4px;
      }
      
      .payment-card {
        font-size: 14px;
        color: #999;
      }
    }
    
    .payment-check {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 2px solid #eee;
    }
  }
}

.confirm-btn {
  width: 90%;
  margin: 32px auto 0 auto;
  display: block;
  background: #1bc47d;
  color: #fff;
  border: none;
  border-radius: 12px;
  font-size: 20px;
  font-weight: bold;
  padding: 14px 0;
  letter-spacing: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
  
  &:hover:not(:disabled) {
    background: #18b371;
  }
}
</style>