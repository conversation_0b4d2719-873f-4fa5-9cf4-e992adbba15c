<template>
  <div class="profile-page">
    <div class="profile-header">
      <button class="back-btn" @click="goBack">←</button>
      <span>{{ $t('profile.title') }}</span>
    </div>
    <div class="profile-main">
      <div class="avatar-container">
        <img class="profile-avatar" :src="profile.avatar || defaultAvatar" alt="avatar" />
        <button class="avatar-edit-btn" @click="$refs.fileInput.click()">✏️</button>
        <input 
          ref="fileInput"
          type="file" 
          accept="image/*" 
          style="display: none" 
          @change="handleAvatarChange"
        >
      </div>
      <div v-if="avatarUpdateSuccess" class="avatar-success-msg">{{ $t('profile.success.avatarUpdated') }}</div>
      <div v-if="avatarUpdateError" class="avatar-error-msg">{{ $t('profile.errors.updateFailed') }}</div>
      <div class="profile-basic">
        <div class="nickname">{{ profile.nickname }}</div>
        <div class="uid">{{ $t('profile.uid', { uid: profile.uid }) }}</div>
      </div>
      <div class="profile-divider"></div>
      <div class="profile-info-block">
        <div class="profile-row">
          <span class="label">{{ $t('profile.fields.nickname') }}</span>
          <span class="value">
            <template v-if="editField !== 'nickname'">
              {{ profile.nickname }}
              <button class="edit-btn" @click="startEdit('nickname')">✏️</button>
            </template>
            <template v-else>
              <input v-model="tempValue" class="edit-input" maxlength="12" />
              <button class="save-btn" @click="saveEdit('nickname')" :disabled="isSaving">
                {{ isSaving ? $t('profile.actions.saving') : $t('profile.actions.save') }}
              </button>
              <button class="cancel-btn" @click="cancelEdit">{{ $t('profile.actions.cancel') }}</button>
            </template>
          </span>
        </div>
        <div class="profile-row">
          <span class="label">{{ $t('profile.fields.uid') }}</span><span class="value">{{ profile.uid }}</span>
        </div>
        <div class="profile-row">
          <span class="label">{{ $t('profile.fields.gender') }}</span>
          <span class="value">
            <template v-if="editField !== 'gender'">
              {{ genderMap[profile.gender] ?? $t('profile.genderOptions.secret') }}
              <button class="edit-btn" @click="startEdit('gender')">✏️</button>
            </template>
            <template v-else>
              <select v-model="tempValue" class="edit-input">
                <option value="male">{{ $t('profile.genderOptions.male') }}</option>
                <option value="female">{{ $t('profile.genderOptions.female') }}</option>
                <option value="secret">{{ $t('profile.genderOptions.secret') }}</option>
              </select>
              <button class="save-btn" @click="saveEdit('gender')" :disabled="isSaving">
                {{ isSaving ? $t('profile.actions.saving') : $t('profile.actions.save') }}
              </button>
              <button class="cancel-btn" @click="cancelEdit">{{ $t('profile.actions.cancel') }}</button>
            </template>
          </span>
        </div>
        <div class="profile-row">
          <span class="label">{{ $t('profile.fields.birthday') }}</span>
          <span class="value">
            <template v-if="editField !== 'birthday'">
              {{ profile.birthday }}
              <button class="edit-btn" @click="startEdit('birthday')">✏️</button>
            </template>
            <template v-else>
              <input v-model="tempValue" type="date" class="edit-input" />
              <button class="save-btn" @click="saveEdit('birthday')" :disabled="isSaving">
                {{ isSaving ? $t('profile.actions.saving') : $t('profile.actions.save') }}
              </button>
              <button class="cancel-btn" @click="cancelEdit">{{ $t('profile.actions.cancel') }}</button>
            </template>
          </span>
        </div>
        <div class="profile-row">
          <span class="label">{{ $t('profile.fields.realName') }}</span>
          <span class="value" :class="{ 'passed': profile.real_name, 'warning': !profile.real_name }">
            {{ profile.real_name ? $t('profile.status.verified') : $t('profile.status.unverified') }}
          </span>
        </div>
        <div class="profile-row">
          <span class="label">{{ $t('profile.fields.vipLevel') }}</span>
          <span class="value vip">
            {{ profile.role === '2' ? `${$t('mine.broker')}V${profile.level}` : $t('mine.user') }}
          </span>
        </div>
        <div class="profile-row">
          <span class="label">{{ $t('profile.fields.phone') }}</span>
          <span class="value">
            <template v-if="editField !== 'mobile'">
              {{ profile.mobile }}
              <button class="edit-btn" @click="startEdit('mobile')">✏️</button>
            </template>
            <template v-else>
              <input v-model="tempValue" class="edit-input" maxlength="15" pattern="[0-9]+" />
              <button class="save-btn" @click="saveEdit('mobile')" :disabled="isSaving">
                {{ isSaving ? $t('profile.actions.saving') : $t('profile.actions.save') }}
              </button>
              <button class="cancel-btn" @click="cancelEdit">{{ $t('profile.actions.cancel') }}</button>
            </template>
          </span>
        </div>
        <div class="profile-row"><span class="label">{{ $t('profile.fields.email') }}</span><span class="value">{{ profile.account }}</span></div>
        <div class="profile-row"><span class="label">{{ $t('profile.fields.registerTime') }}</span><span class="value">{{ profile.create_time }}</span></div>
        <div class="profile-row">
          <span class="label">{{ $t('profile.fields.signature') }}</span>
          <span class="value">
            <template v-if="editField !== 'signature'">
              {{ profile.signature }}
              <button class="edit-btn" @click="startEdit('signature')">✏️</button>
            </template>
            <template v-else>
              <input v-model="tempValue" class="edit-input" maxlength="30" />
              <button class="save-btn" @click="saveEdit('signature')" :disabled="isSaving">
                {{ isSaving ? $t('profile.actions.saving') : $t('profile.actions.save') }}
              </button>
              <button class="cancel-btn" @click="cancelEdit">{{ $t('profile.actions.cancel') }}</button>
            </template>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加toast通知 -->
  <transition name="fade">
    <div v-if="showToast" class="toast" :class="toastType">
      {{ toastMessage }}
    </div>
  </transition>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import userApi from '../api/user'
import defaultAvatar from '../assets/avatars/default.png'

// 添加toast提示
const showToast = ref(false)
const toastMessage = ref('')
const toastType = ref('success') // 'success' or 'error'

function showToastMessage(message, type = 'success') {
  toastMessage.value = message
  toastType.value = type
  showToast.value = true
  setTimeout(() => {
    showToast.value = false
  }, 3000)
}

const { t, te } = useI18n()

// 检查并添加缺失的翻译键
if (!te('profile.validation.invalidImageType')) {
  console.warn('Missing translation key: profile.validation.invalidImageType')
}
if (!te('profile.validation.imageTooLarge')) {
  console.warn('Missing translation key: profile.validation.imageTooLarge')
}
if (!te('profile.success.avatarUpdated')) {
  console.warn('Missing translation key: profile.success.avatarUpdated')
}
if (!te('profile.errors.uploadFailed')) {
  console.warn('Missing translation key: profile.errors.uploadFailed')
}
const router = useRouter()
const goBack = () => router.back()
const fileInput = ref(null)

async function handleAvatarChange(e) {
  avatarUpdateSuccess.value = false
  avatarUpdateError.value = false
  const file = e.target.files[0]
  if (!file) return
  
  // 验证图片类型和大小
  if (!file.type.startsWith('image/')) {
    showToastMessage(t('profile.validation.invalidImageType'), 'error')
    return
  }
  
  if (file.size > 5 * 1024 * 1024) { // 5MB限制
    showToastMessage(t('profile.validation.imageTooLarge'), 'error')
    return
  }

  try {
    // 创建预览URL
    const previewUrl = URL.createObjectURL(file)
    
    // 创建FormData
    const formData = new FormData()
    formData.append('file', file)
    
    // 调用API上传头像到/upload/image
    const response = await userApi.uploadImage(formData)
    
    if (response && response.data.uri) {
      // 更新本地头像 - 使用后端返回的uri字段
      const uri = response.data.uri || previewUrl
      profile.value.avatar = uri
      
      // 立即调用修改用户信息接口，avatar字段
      try {
        await userApi.updateUserInfo({ avatar: uri })
        avatarUpdateSuccess.value = true
        setTimeout(() => { avatarUpdateSuccess.value = false }, 2000)
      } catch (e) {
        avatarUpdateError.value = true
        setTimeout(() => { avatarUpdateError.value = false }, 2000)
      }
      
      // 更新本地存储
      const USER_CACHE_KEY = 'user_cache'
      const userStr = localStorage.getItem(USER_CACHE_KEY)
      if (userStr) {
        const user = JSON.parse(userStr)
        user.avatar = uri
        localStorage.setItem(USER_CACHE_KEY, JSON.stringify(user))
      }
      // 不再showToastMessage
    } else {
      avatarUpdateError.value = true
      setTimeout(() => { avatarUpdateError.value = false }, 2000)
      showToastMessage(response?.msg || t('profile.errors.uploadFailed'), 'error')
    }
  } catch (error) {
    avatarUpdateError.value = true
    setTimeout(() => { avatarUpdateError.value = false }, 2000)
    console.error('Error uploading avatar:', error)
    showToastMessage(t('profile.errors.uploadFailed'), 'error')
  } finally {
    // 重置文件输入
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
}

// 在profile对象初始值中补充account、create_time、role、level、is_auth、real_name字段
const profile = ref({
  nickname: '',
  gender: '',
  birthday: '',
  signature: '',
  avatar: '',
  uid: '',
  account: '',
  create_time: '',
  role: '',
  level: '',
  is_auth: false,
  real_name: '',
  email: '',
  mobile: '',
  age: '',
  remark: '',
  sex: '',
})

onMounted(() => {
  const USER_CACHE_KEY = 'user_cache'
  const userStr = localStorage.getItem(USER_CACHE_KEY)
  if (userStr) {
    try {
      const user = JSON.parse(userStr)
      profile.value.nickname = user.nickname || ''
      profile.value.gender = user.sex || ''
      profile.value.birthday = user.birthday || ''
      profile.value.signature = user.remark || ''
      profile.value.avatar = user.avatar || ''
      profile.value.uid = user.id ? String(user.id) : ''
      profile.value.account = user.account || ''
      profile.value.create_time = user.create_time || ''
      profile.value.role = user.role || ''
      profile.value.level = user.levle !== undefined ? user.levle : ''
      profile.value.is_auth = !!user.real_name
      profile.value.real_name = user.real_name || ''
      profile.value.mobile = user.mobile || ''
      profile.value.age = user.age || ''
      profile.value.remark = user.remark || ''
      profile.value.sex = user.sex || ''
    } catch (e) {
      // ignore parse error, use mock
    }
  }
})
const editField = ref('')
const tempValue = ref('')
const genderMap = {
  0: t('profile.genderOptions.secret'),
  1: t('profile.genderOptions.male'),
  2: t('profile.genderOptions.female'),
}
const genderValueMap = {
  secret: 0,
  male: 1,
  female: 2,
}
function startEdit(field) {
  editField.value = field
  if (field === 'gender' || field === 'sex') {
    // profile.gender 可能是数字
    if (profile.value.gender === 1) tempValue.value = 'male'
    else if (profile.value.gender === 2) tempValue.value = 'female'
    else tempValue.value = 'secret'
  } else {
    tempValue.value = profile.value[field]
  }
}
function cancelEdit() {
  editField.value = ''
  tempValue.value = ''
}
const isSaving = ref(false)
const avatarUpdateSuccess = ref(false)
const avatarUpdateError = ref(false)

function getAgeFromBirthday(birthdayStr) {
  if (!birthdayStr) return '';
  const today = new Date();
  const birthDate = new Date(birthdayStr);
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
}

async function saveEdit(field) {
  // 如果是手机号码，进行格式验证
      if (field === 'mobile' && tempValue.value) {
        // 简单验证：只允许数字，长度在7-15之间
        const phoneRegex = /^[0-9]{7,15}$/
        if (!phoneRegex.test(tempValue.value)) {
          showToastMessage(t('profile.validation.invalidPhone'), 'error')
          return
        }
      }
  
  // 准备API请求参数
  const params = {}
  
  // 根据不同字段设置不同的参数
  if (field === 'nickname') params.nickname = tempValue.value
  if (field === 'gender' || field === 'sex') params.sex = genderValueMap[tempValue.value]
  if (field === 'birthday') params.age = getAgeFromBirthday(tempValue.value)
  if (field === 'signature' || field === 'remark') params.remark = tempValue.value
  if (field === 'mobile') params.mobile = tempValue.value
  
  // 设置保存状态
  isSaving.value = true
  
  try {
    // 调用API保存数据
    const response = await userApi.updateUserInfo(params)
    
    if (response && response.code == 1) { // 用==兼容字符串/数字
      // API调用成功，更新本地数据
      if (field === 'gender' || field === 'sex') {
        profile.value.gender = genderValueMap[tempValue.value]
        profile.value.sex = genderValueMap[tempValue.value]
      } else {
        profile.value[field] = tempValue.value
        if (field === 'birthday') profile.value.age = getAgeFromBirthday(tempValue.value)
      }
      
      // 更新本地存储
      const USER_CACHE_KEY = 'user_cache'
      const userStr = localStorage.getItem(USER_CACHE_KEY)
      if (userStr) {
        const user = JSON.parse(userStr)
        // 更新对应字段
        if (field === 'nickname') user.nickname = tempValue.value
        if (field === 'gender' || field === 'sex') user.sex = genderValueMap[tempValue.value]
        if (field === 'birthday') {
          user.birthday = tempValue.value
          user.age = getAgeFromBirthday(tempValue.value)
        }
        if (field === 'signature' || field === 'remark') user.remark = tempValue.value
        if (field === 'mobile') user.mobile = tempValue.value
        
        // 保存回本地存储
        localStorage.setItem(USER_CACHE_KEY, JSON.stringify(user))
      }
      
      // 如果show不等于1，则显示成功提示
      if (response.show != 1) {
        showToastMessage(response.msg || t('profile.success.updated'))
      }
    } else {
      // API调用失败，显示错误信息
      const errorMsg = response?.msg || t('profile.errors.updateFailed')
      showToastMessage(errorMsg, 'error')
      console.error('Failed to update user info:', response)
    }
  } catch (e) {
    // 处理异常
    showToastMessage(t('profile.errors.updateFailed'), 'error')
    console.error('Error updating user info:', e)
  } finally {
    // 无论成功失败，都重置状态
    isSaving.value = false
    editField.value = ''
  }
}
</script>
<style scoped lang="scss">
.profile-page {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.profile-header {
  width: 100%;
  max-width: 420px;
  display: flex;
  align-items: center;
  padding: 18px 0 12px 0;
  font-size: 18px;
  font-weight: 700;
  color: #222;
  background: none;
  position: relative;
  justify-content: center;
}
.back-btn {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 22px;
  color: #228c22;
  cursor: pointer;
  padding: 0 16px;
}
.profile-main {
  width: 100%;
  max-width: 420px;
  background: #fff;
  border-radius: 28px;
  box-shadow: 0 6px 32px rgba(34,140,34,0.10);
  margin: 0 auto;
  padding: 40px 28px 28px 28px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.avatar-container {
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}

.profile-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  border: 4px solid #f8f9fa;
  transition: all 0.3s ease;
}

.avatar-edit-btn {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 28px;
  height: 28px;
  background: #1bc47d;
  color: white;
  border: 2px solid white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  
  &:hover {
    background: #18b06f;
    transform: scale(1.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
}
.profile-basic {
  text-align: center;
  margin-bottom: 16px;
  .nickname {
    font-size: 24px;
    font-weight: 700;
    color: #222;
    margin-bottom: 4px;
  }
  .uid {
    font-size: 14px;
    color: #aaa;
    letter-spacing: 1px;
  }
}
.profile-divider {
  width: 60%;
  height: 2px;
  background: #f0f0f0;
  margin: 24px 0 24px 0;
  border-radius: 1px;
}
.profile-info-block {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.profile-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  color: #333;
  padding: 8px 14px;
  background: #f8f9fa;
  border-radius: 10px;
  min-height: 24px;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f0f2f5;
  }
}
.label {
  color: #666;
  font-size: 15px;
  font-weight: 500;
}
.value {
  color: #222;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}
.value.passed {
  color: #1bc47d;
  background: #e8f5e9;
  border-radius: 8px;
  padding: 4px 12px;
  font-size: 14px;
  font-weight: 700;
}
.value.warning {
  color: #ff9800;
  background: #fff3e0;
  border-radius: 8px;
  padding: 4px 12px;
  font-size: 14px;
  font-weight: 700;
}
.value.vip {
  color: #8e44ad;
  background: #f3e5f5;
  border-radius: 8px;
  padding: 4px 12px;
  font-size: 14px;
  font-weight: 700;
}
.edit-btn {
  background: none;
  border: none;
  color: #1bc47d;
  cursor: pointer;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  opacity: 0.7;
  
  &:hover {
    background: rgba(27, 196, 125, 0.1);
    opacity: 1;
  }
}

.save-btn, .cancel-btn {
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.save-btn {
  background: #1bc47d;
  color: white;
  box-shadow: 0 2px 8px rgba(27, 196, 125, 0.2);
  
  &:hover:not(:disabled) {
    background: #18b06f;
    box-shadow: 0 4px 12px rgba(27, 196, 125, 0.3);
    transform: translateY(-1px);
  }
  
  &:disabled {
    background: #a8e2c9;
    cursor: not-allowed;
  }
}

.cancel-btn {
  background: #f0f2f5;
  color: #666;
  margin-left: 8px;
  
  &:hover {
    background: #e0e2e5;
    color: #444;
  }
}

.edit-input {
  font-size: 15px;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-right: 8px;
  outline: none;
  transition: all 0.2s ease;
  width: 100%;
  max-width: 260px;
  min-width: 80px;
  box-sizing: border-box;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

  &:focus {
    border-color: #1bc47d;
    box-shadow: 0 0 0 2px rgba(27, 196, 125, 0.1);
  }
}

/* 添加编辑状态的过渡动画 */
.value {
  transition: all 0.3s ease;
}
/* Toast通知样式 */
.toast {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-width: 80%;
  text-align: center;
  animation: slideUp 0.3s ease;
}

.toast.success {
  background: #1bc47d;
}

.toast.error {
  background: #ff5252;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@keyframes slideUp {
  from {
    transform: translate(-50%, 20px);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}
.avatar-success-msg {
  color: #1bc47d;
  text-align: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}
.avatar-error-msg {
  color: #ff5252;
  text-align: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}
</style>