<template>
  <div class="rank-page">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <svg width="20" height="20" viewBox="0 0 24 24"><circle cx="11" cy="11" r="7" stroke="#bbb" stroke-width="2" fill="none"/><line x1="18" y1="18" x2="22" y2="22" stroke="#bbb" stroke-width="2"/></svg>
      <span class="hot-icon">🔥</span>
      <input type="text" :placeholder="$t('rank.searchPlaceholder')" />
    </div>

    <!-- 主Tab导航 -->
    <div class="main-tabs">
      <span @click="$router.push('/market')">{{ $t('rank.tabs.market') }}</span>
      <span @click="$router.push('/dynamic')">{{ $t('rank.tabs.dynamic') }}</span>
      <span class="active">{{ $t('rank.tabs.rank') }}</span>
    </div>

    <!-- 二级tab和筛选 -->
    <div class="rank-sub-tabs">
      <span :class="{active: rankTab==='rate'}" @click="rankTab='rate'">{{ $t('rank.subTabs.rate') }}</span>
      <span :class="{active: rankTab==='amount'}" @click="rankTab='amount'">{{ $t('rank.subTabs.amount') }}</span>
      <span :class="{active: rankTab==='asset'}" @click="rankTab='asset'">{{ $t('rank.subTabs.asset') }}</span>
      <span class="rank-filter">{{ $t('rank.filter') }}<svg width="14" height="14" viewBox="0 0 24 24" style="vertical-align:middle;"><path d="M7 10l5 5 5-5" stroke="#888" stroke-width="2" fill="none" stroke-linecap="round"/></svg></span>
    </div>

    <!-- 榜单列表 -->
    <div class="rank-list">
      <div class="rank-item" v-for="item in rankList" :key="item.id">
        <div class="rank-avatar" :style="item.avatar ? '' : 'background:'+item.bgColor">
          <img v-if="item.avatar" :src="item.avatar" />
          <span v-else>{{ item.initial }}</span>
        </div>
        <div class="rank-info">
          <div class="rank-title">
            <span class="rank-name">{{ item.name }}</span>
            <span v-for="tag in item.tags" :key="tag.text" class="rank-tag" :class="tag.type">{{ tag.text }}</span>
          </div>
          <div class="rank-meta">
            <div class="rank-meta-col">
              <div class="rank-meta-label">{{ $t('rank.labels.rate') }}</div>
              <div class="rank-meta-value rate">{{ item.rate }}</div>
            </div>
            <div class="rank-meta-col">
              <div class="rank-meta-label">{{ $t('rank.labels.amount') }}</div>
              <div class="rank-meta-value amount">{{ item.amount }}</div>
            </div>
            <div class="rank-meta-col">
              <div class="rank-meta-label">{{ $t('rank.labels.asset') }}</div>
              <div class="rank-meta-value asset">{{ item.asset }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import TabBar from '../components/TabBar.vue'

const { t } = useI18n()
const rankTab = ref('amount')

// 扩展的榜单数据
const rankList = [
  {
    id: 1,
    avatar: '',
    initial: '炒',
    bgColor: '#FFA54B',
    name: t('common.mockUsers.chaobi'),
    tags: [{ text: '空 2.96x', type: 'short' }],
    rate: '+429.34%',
    amount: '+$8,395,944',
    asset: '$2,484,975'
  },
  {
    id: 2,
    avatar: '',
    initial: 't',
    bgColor: '#C96B6B',
    name: 'tal***@proton.me',
    tags: [{ text: '多 0.05x', type: 'long' }],
    rate: '+10.42%',
    amount: '+$7,341,596',
    asset: '$39,378,087'
  },
  {
    id: 3,
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    initial: '',
    bgColor: '',
    name: t('common.mockUsers.kunpeng'),
    tags: [{ text: '多 0.11x', type: 'long' }, { text: '空 0.01x', type: 'short' }],
    rate: '+302.47%',
    amount: '+$5,772,342',
    asset: '$5,977,280'
  },
  {
    id: 4,
    avatar: '',
    initial: '金',
    bgColor: '#FFA54B',
    name: t('common.mockUsers.jinfa'),
    tags: [],
    rate: '+96.54%',
    amount: '+$5,758,694',
    asset: '$1,021,006'
  },
  {
    id: 5,
    avatar: '',
    initial: 'C',
    bgColor: 'linear-gradient(135deg, #FFD93D, #FF6B6B)',
    name: 'crypto***@gmail.com',
    tags: [{ text: '多 1.5x', type: 'long' }],
    rate: '+87.23%',
    amount: '+$4,123,456',
    asset: '$8,765,432'
  },
  {
    id: 6,
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    initial: '',
    bgColor: '',
    name: 'alice***@yahoo.com',
    tags: [{ text: '空 0.8x', type: 'short' }],
    rate: '+65.78%',
    amount: '+$3,456,789',
    asset: '$6,543,210'
  }
]
</script>

<style scoped lang="scss">
.rank-page {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
  padding-bottom: 60px;
}

.page-header {
  text-align: center;
  padding: 24px 20px 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  
  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .page-subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
    font-weight: 400;
  }
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f6f6f6;
  border-radius: 18px;
  margin: 16px 16px 0 16px;
  padding: 8px 12px;
  svg { margin-right: 6px; }
  .hot-icon { margin-right: 4px; }
  input {
    border: none;
    background: transparent;
    font-size: 15px;
    flex: 1;
    outline: none;
  }
}

.main-tabs {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 12px 0 0 0;
  border-bottom: 1.5px solid #eee;
  span {
    font-size: 16px;
    color: #888;
    padding: 10px 0;
    margin: 0 12px;
    cursor: pointer;
    position: relative;
    &.active {
      color: #111;
      font-weight: bold;
      &::after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: -2px;
        transform: translateX(-50%);
        width: 32px;
        height: 3px;
        background: #111;
        border-radius: 2px;
      }
    }
  }
}

.rank-sub-tabs {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 12px 0 0 0;
  padding: 0 12px 0 16px;
  font-size: 12px;
  span {
    color: #bbb;
    background: #f5f5f5;
    padding: 10px 24px 10px 24px;
    border-radius: 16px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s, color 0.2s;
    box-shadow: none;
    &.active {
      background: #fff;
      color: #111;
      font-weight: bold;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    }
    &.rank-filter {
      margin-left: auto;
      color: #222;
      background: none;
      padding: 0;
      font-size: 15px;
      display: flex;
      align-items: center;
      font-weight: normal;
      box-shadow: none;
      svg {
        margin-left: 2px;
      }
    }
  }
}

.rank-list {
  margin: 18px 0 0 0;
  .rank-item {
    display: flex;
    align-items: flex-start;
    padding: 18px 16px 12px 16px;
    border-bottom: 1px solid #f2f2f2;
    .rank-avatar {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      background: #eee;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 22px;
      font-weight: bold;
      color: #fff;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }
    }
    .rank-info {
      flex: 1;
      .rank-title {
        display: flex;
        align-items: center;
        gap: 8px;
        .rank-name {
          font-weight: bold;
          font-size: 17px;
        }
        .rank-tag {
          font-size: 13px;
          padding: 2px 8px;
          border-radius: 8px;
          margin-left: 2px;
          &.long {
            background: #f8d7da;
            color: #d9534f;
          }
          &.short {
            background: #d4f8e8;
            color: #1bc47d;
          }
        }
      }
      .rank-meta {
        display: flex;
        gap: 32px;
        margin-top: 8px;
        .rank-meta-col {
          .rank-meta-label {
            color: #aaa;
            font-size: 14px;
          }
          .rank-meta-value {
            font-size: 17px;
            font-weight: bold;
            margin-top: 2px;
            &.rate, &.amount {
              color: #f66;
            }
            &.asset {
              color: #111;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    padding: 20px 16px 12px;
    
    .page-title {
      font-size: 24px;
    }
    
    .page-subtitle {
      font-size: 14px;
    }
  }
  
  .search-bar {
    padding: 8px 12px;
  }
  
  .main-tabs {
    padding: 0 16px 12px;
    
    span {
      font-size: 14px;
      padding: 10px 16px;
    }
  }
  
  .rank-sub-tabs {
    padding: 0 16px 16px;
    
    span {
      font-size: 13px;
      padding: 6px 12px;
    }
  }
  
  .rank-list {
    padding: 0 16px;
    
    .rank-item {
      padding: 16px;
      gap: 12px;
      
      .rank-avatar {
        width: 40px;
        height: 40px;
        font-size: 16px;
      }
      
      .rank-meta .rank-meta-value {
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .rank-list .rank-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    
    .rank-meta {
      width: 100%;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style> 