<template>
  <div class="register-page">
    <!-- 装饰背景元素 -->
    <div class="decoration-circle circle-1"></div>
    <div class="decoration-circle circle-2"></div>
    <div class="decoration-circle circle-3"></div>

    <div class="register-container">
      <!-- 返回按钮 -->
      <router-link to="/" class="back-btn">
        <span class="back-arrow">←</span>
        {{ $t('common.back') }}
      </router-link>

      <div class="form-container">
        <h1 class="title">{{ $t('register.title') }}</h1>
        <p class="subtitle">{{ $t('register.subtitle') }}</p>

        <form @submit.prevent="handleSubmit" class="register-form">
          <!-- 邮箱输入框 -->
          <div class="form-group" :class="{ 'has-error': emailError }">
            <label for="email">{{ $t('register.email') }}</label>
            <div class="input-wrapper">
              <i class="input-icon">📧</i>
              <input
                type="email"
                id="email"
                v-model="email"
                @input="debouncedValidateEmail"
                @blur="validateEmail"
                :placeholder="$t('register.emailPlaceholder')"
                :class="{ 'has-error': emailError, 'has-success': email && !emailError }"
                required
              />
              <i v-if="email && !emailError" class="success-icon">✓</i>
            </div>
            <span class="error-message" v-if="emailError">{{ emailError }}</span>
          </div>

          <!-- 密码输入框 -->
          <div class="form-group" :class="{ 'has-error': passwordError }">
            <label for="password">{{ $t('register.password') }}</label>
            <div class="input-wrapper">
              <i class="input-icon">🔒</i>
              <input
                :type="showPassword ? 'text' : 'password'"
                id="password"
                v-model="password"
                @input="debouncedValidatePassword"
                @blur="validatePassword"
                :placeholder="$t('register.passwordPlaceholder')"
                :class="{ 'has-error': passwordError, 'has-success': password && !passwordError }"
                required
              />
              <button
                type="button"
                class="toggle-password"
                @click="showPassword = !showPassword"
              >
                {{ showPassword ? '👁️' : '👁️‍🗨️' }}
              </button>
              <i v-if="password && !passwordError" class="success-icon">✓</i>
            </div>
            <!-- 密码强度指示器 -->
            <div class="password-strength" v-if="password">
              <div class="strength-bars">
                <div
                  v-for="n in 4"
                  :key="n"
                  class="strength-bar"
                  :class="{ active: passwordStrength >= n }"
                ></div>
              </div>
              <span class="strength-text">{{ strengthText }}</span>
            </div>
            <span class="error-message" v-if="passwordError">{{ passwordError }}</span>
          </div>

          <!-- 确认密码输入框 -->
          <div class="form-group" :class="{ 'has-error': confirmError }">
            <label for="confirm">{{ $t('register.confirm') }}</label>
            <div class="input-wrapper">
              <i class="input-icon">🔒</i>
              <input
                :type="showConfirm ? 'text' : 'password'"
                id="confirm"
                v-model="confirmPassword"
                @input="validateConfirm"
                @blur="validateConfirm"
                :placeholder="$t('register.confirmPlaceholder')"
                :class="{ 'has-error': confirmError, 'has-success': confirmPassword && !confirmError }"
                required
              />
              <button
                type="button"
                class="toggle-password"
                @click="showConfirm = !showConfirm"
              >
                {{ showConfirm ? '👁️' : '👁️‍🗨️' }}
              </button>
              <i v-if="confirmPassword && !confirmError" class="success-icon">✓</i>
            </div>
            <span class="error-message" v-if="confirmError">{{ confirmError }}</span>
          </div>

          <!-- 邀请码输入框 -->
          <div class="form-group" :class="{ 'has-error': inviteCodeError }">
            <label for="inviteCode">{{ getI18n('register.inviteCode') }}</label>
            <div class="input-wrapper">
              <i class="input-icon">🎟️</i>
              <input
                type="text"
                id="inviteCode"
                v-model="inviteCode"
                @input="validateInviteCode"
                @blur="validateInviteCode"
                :placeholder="getI18n('register.inviteCodePlaceholder')"
                :class="{ 'has-error': inviteCodeError, 'has-success': inviteCode && !inviteCodeError }"
              />
              <i v-if="inviteCode && !inviteCodeError" class="success-icon">✓</i>
            </div>
            <span class="error-message" v-if="inviteCodeError">{{ inviteCodeError }}</span>
          </div>

          <!-- 注册按钮上方增加全局错误提示 -->
          <span class="error-message" v-if="generalError">{{ generalError }}</span>
          <!-- 注册按钮 -->
          <button
            type="submit"
            class="register-btn"
            :disabled="!isFormValid || isLoading"
          >
            <span v-if="!isLoading">{{ $t('register.createAccount') }}</span>
            <span v-else class="loading-spinner"></span>
          </button>
        </form>

        <!-- 分隔线 -->
        <div class="divider">
          <span>{{ $t('register.or') }}</span>
        </div>
        <!-- 登录链接 -->
        <p class="login-link">
          {{ $t('register.hasAccount') }}
          <router-link to="/login">{{ $t('register.loginNow') }}</router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import googleIcon from '@/assets/google.svg'
import appleIcon from '@/assets/apple.svg'
import userApi from '@/api/user'

const { t, te } = useI18n()
const router = useRouter()

// 表单数据
const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const inviteCode = ref('')
const showPassword = ref(false)
const showConfirm = ref(false)
const isLoading = ref(false)

// 错误信息
const emailError = ref('')
const passwordError = ref('')
const confirmError = ref('')
const inviteCodeError = ref('')
const generalError = ref('') // 新增

// 表单验证
const validateEmail = () => {
  // 更严格的邮箱格式验证
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  
  if (!email.value.trim()) {
    emailError.value = t('register.emailRequired')
  } else if (!emailRegex.test(email.value.trim())) {
    emailError.value = t('register.emailInvalid')
  } else if (email.value.length > 100) {
    emailError.value = t('register.emailTooLong')
  } else {
    emailError.value = ''
  }
}

const validatePassword = () => {
  if (!password.value.trim()) {
    passwordError.value = t('register.passwordRequired')
  } else if (password.value.length < 8) {
    passwordError.value = t('register.passwordLength')
  } else if (password.value.length > 50) {
    passwordError.value = t('register.passwordTooLong')
  } else if (!/[A-Za-z]/.test(password.value)) {
    passwordError.value = t('register.passwordNoLetter')
  } else if (!/[0-9]/.test(password.value)) {
    passwordError.value = t('register.passwordNoNumber')
  } else {
    passwordError.value = ''
  }
  validateConfirm()
}

const validateConfirm = () => {
  if (!confirmPassword.value.trim()) {
    confirmError.value = t('register.confirmRequired')
  } else if (confirmPassword.value !== password.value) {
    confirmError.value = t('register.confirmMismatch')
  } else {
    confirmError.value = ''
  }
}

const validateInviteCode = () => {
  // 移除格式校验，始终不报错
  inviteCodeError.value = ''
}

// 密码强度计算
const passwordStrength = computed(() => {
  if (!password.value) return 0
  let strength = 0
  if (password.value.length >= 8) strength++
  if (/[A-Z]/.test(password.value)) strength++
  if (/[0-9]/.test(password.value)) strength++
  if (/[^A-Za-z0-9]/.test(password.value)) strength++
  return strength
})

const strengthText = computed(() => {
  const texts = [t('register.strength.weak'), t('register.strength.normal'), t('register.strength.medium'), t('register.strength.strong')]
  return texts[passwordStrength.value - 1] || ''
})

// 表单有效性
const isFormValid = computed(() => {
  return (
    email.value &&
    password.value &&
    confirmPassword.value &&
    !emailError.value &&
    !passwordError.value &&
    !confirmError.value
  )
})

// 防抖函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 防抖验证函数
const debouncedValidateEmail = debounce(validateEmail, 500)
const debouncedValidatePassword = debounce(validatePassword, 500)
const debouncedValidateInviteCode = debounce(validateInviteCode, 500)

// 确保国际化文本可用 - 提供默认值
const i18nFallback = {
  'register.inviteCode': '邀请码',
  'register.inviteCodePlaceholder': '请输入邀请码（可选）', 
  'register.inviteCodeInvalid': '邀请码格式不正确（6-12位字母数字）'
}

// 使用带默认值的翻译函数
const getI18n = (key) => te(key) ? t(key) : i18nFallback[key]

// 表单提交
const handleSubmit = async () => {
  validateEmail()
  validatePassword()
  validateConfirm()
  validateInviteCode()
  generalError.value = ''
  
  if (!isFormValid.value || (inviteCode.value && inviteCodeError.value)) return
  
  try {
    isLoading.value = true
    // 只发送邮箱验证码，不注册
    const res = await userApi.sendEmailVerificationCode({
      email: email.value,
      inviteCode: inviteCode.value.trim() || undefined
    })
    
    if (!res.success) {
      throw { response: { data: { msg: res.msg || t('register.failed') } } }
    }
    
    // 存储注册信息到 sessionStorage
    sessionStorage.setItem('register_email', email.value)
    sessionStorage.setItem('register_password', password.value)
    if (inviteCode.value.trim()) {
      sessionStorage.setItem('register_invite_code', inviteCode.value.trim())
    }
    // 跳转到验证码页面，仅传递 email
    router.push({
      path: '/verify',
      query: { email: email.value }
    })
  } catch (error) {
    generalError.value = error.response?.data?.msg || t('register.failed')
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped lang="scss">
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 20px;
}

// 装饰圆圈样式（与RegisterSuccess.vue保持一致）
.decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  
  &.circle-1 {
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, #007bff, #00ff88);
    top: -100px;
    right: -100px;
    animation: float 8s ease-in-out infinite;
  }
  
  &.circle-2 {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, #ff3366, #ff9933);
    bottom: -50px;
    left: -50px;
    animation: float 6s ease-in-out infinite reverse;
  }
  
  &.circle-3 {
    width: 150px;
    height: 150px;
    background: linear-gradient(45deg, #6610f2, #6f42c1);
    top: 50%;
    right: -75px;
    animation: float 7s ease-in-out infinite;
  }
}

.register-container {
  width: 100%;
  max-width: 480px;
  position: relative;
  z-index: 1;
}

.back-btn {
  display: inline-flex;
  align-items: center;
  color: #666;
  text-decoration: none;
  font-size: 16px;
  margin-bottom: 24px;
  transition: color 0.3s ease;
  
  .back-arrow {
    margin-right: 8px;
    transition: transform 0.3s ease;
  }
  
  &:hover {
    color: #111;
    
    .back-arrow {
      transform: translateX(-4px);
    }
  }
}

.form-container {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.6s ease-out forwards;
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #111;
  margin-bottom: 8px;
}

.subtitle {
  color: #666;
  font-size: 16px;
  margin-bottom: 32px;
}

.form-group {
  margin-bottom: 24px;
  
  label {
    display: block;
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
  }
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  
  .input-icon {
    position: absolute;
    left: 16px;
    color: #666;
    font-style: normal;
  }
  
  input {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    
    &:focus {
      border-color: #111;
      outline: none;
      box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1);
    }
  }
  
  .toggle-password {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    font-size: 20px;
  }
}

.password-strength {
  margin-top: 8px;
  
  .strength-bars {
    display: flex;
    gap: 4px;
    margin-bottom: 4px;
  }
  
  .strength-bar {
    height: 4px;
    flex: 1;
    background: #e0e0e0;
    border-radius: 2px;
    transition: background-color 0.3s ease;
    
    &.active {
      &:nth-child(1) { background-color: #dc3545; }
      &:nth-child(2) { background-color: #ffc107; }
      &:nth-child(3) { background-color: #28a745; }
      &:nth-child(4) { background-color: #198754; }
    }
  }
  
  .strength-text {
    font-size: 12px;
    color: #666;
  }
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.register-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(45deg, #111 0%, #333 100%);
  color: #fff;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

.divider {
  text-align: center;
  margin: 24px 0;
  position: relative;
  
  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    width: calc(50% - 30px);
    height: 1px;
    background: #e0e0e0;
  }
  
  &::before { left: 0; }
  &::after { right: 0; }
  
  span {
    background: #fff;
    padding: 0 16px;
    color: #666;
    font-size: 14px;
  }
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e0e0e0;
    background: #fff;
    
    img {
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    &.google {
      color: #4285F4;
      border-color: #4285F4;
      background: rgba(66, 133, 244, 0.05);
    }
    
    &.apple {
      color: #000;
      border-color: #000;
      background: rgba(0, 0, 0, 0.05);
    }
  }
}

.login-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
  font-size: 14px;
  
  a {
    color: #111;
    font-weight: 600;
    text-decoration: none;
    transition: color 0.3s ease;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

// 表单验证状态样式
.form-group.has-error {
  .input-wrapper input {
    border-color: #dc3545;
  }
}

.input-wrapper {
  position: relative;
  
  input.has-error {
    border-color: #dc3545 !important;
    background-color: #fff5f5;
    animation: shake 0.5s ease-in-out;
  }
  
  input.has-success {
    border-color: #28a745 !important;
    background-color: #f8fff9;
  }
  
  .success-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #28a745;
    font-size: 18px;
    font-weight: bold;
    pointer-events: none;
  }
  
  .toggle-password {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    font-size: 20px;
    
    & + .success-icon {
      right: 50px;
    }
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  20%, 60% {
    transform: translateX(-5px);
  }
  40%, 80% {
    transform: translateX(5px);
  }
}

// 动画关键帧
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-container {
    padding: 30px 20px;
  }
  
  .title {
    font-size: 28px;
  }
  
  .subtitle {
    font-size: 14px;
    margin-bottom: 24px;
  }
  
  .register-btn {
    padding: 14px;
  }
}
</style>