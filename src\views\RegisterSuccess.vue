<template>
  <div class="register-success">
    <div class="success-container">
      <!-- 装饰背景元素 -->
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
      
      <!-- 主要内容 -->
      <div class="content-wrapper">
        <div class="success-icon-wrapper">
          <div class="success-icon">
            <svg viewBox="0 0 100 100" class="checkmark">
              <circle cx="50" cy="50" r="45" class="circle" />
              <path d="M30 50 L45 65 L70 35" class="check" />
            </svg>
          </div>
          <div class="success-ring"></div>
          <div class="success-particles">
            <div class="particle" v-for="n in 8" :key="n"></div>
          </div>
        </div>
        <h2 class="title">{{ $t('registerSuccess.title') }}</h2>
        <p class="subtitle">{{ $t('registerSuccess.subtitle') }}</p>
        <router-link to="/login" class="start-btn">
          {{ $t('registerSuccess.startJourney') }}
          <span class="btn-arrow">→</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
</script>

<style scoped lang="scss">
.register-success {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.success-container {
  position: relative;
  width: 100%;
  max-width: 480px;
  padding: 40px 24px;
  z-index: 1;
}

// 装饰圆圈
.decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  
  &.circle-1 {
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, #007bff, #00ff88);
    top: -100px;
    right: -100px;
    animation: float 8s ease-in-out infinite;
  }
  
  &.circle-2 {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, #ff3366, #ff9933);
    bottom: -50px;
    left: -50px;
    animation: float 6s ease-in-out infinite reverse;
  }
  
  &.circle-3 {
    width: 150px;
    height: 150px;
    background: linear-gradient(45deg, #6610f2, #6f42c1);
    top: 50%;
    right: -75px;
    animation: float 7s ease-in-out infinite;
  }
}

.content-wrapper {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  padding: 48px 32px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.6s ease-out forwards;
}

.success-icon-wrapper {
  position: relative;
  width: 180px;
  height: 180px;
  margin: 0 auto 32px;
  
  .success-icon {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
    animation: scaleIn 0.5s ease-out 0.3s both;
    
    .checkmark {
      width: 100%;
      height: 100%;
      
      .circle {
        fill: #28a745;
        stroke: #28a745;
        stroke-width: 2;
        animation: circleDraw 0.6s ease-out 0.3s both;
      }
      
      .check {
        fill: none;
        stroke: #fff;
        stroke-width: 6;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-dasharray: 100;
        stroke-dashoffset: 100;
        animation: checkDraw 0.6s ease-out 0.9s both;
      }
    }
  }
  
  .success-ring {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 3px solid #28a745;
    border-radius: 50%;
    animation: ringPulse 2s ease-out infinite;
  }
  
  .success-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    
    .particle {
      position: absolute;
      width: 8px;
      height: 8px;
      background: #28a745;
      border-radius: 50%;
      top: 50%;
      left: 50%;
      margin: -4px 0 0 -4px;
      animation: particleExplode 1s ease-out 1.2s both;
      
      @for $i from 1 through 8 {
        &:nth-child(#{$i}) {
          animation-delay: #{1.2 + $i * 0.1}s;
          transform: rotate(#{($i - 1) * 45}deg) translateY(-60px);
        }
      }
    }
  }
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #111;
  margin-bottom: 16px;
  animation: fadeIn 0.5s ease-out 0.6s both;
}

.subtitle {
  color: #666;
  font-size: 18px;
  margin-bottom: 40px;
  animation: fadeIn 0.5s ease-out 0.8s both;
}

.start-btn {
  background: linear-gradient(45deg, #111 0%, #333 100%);
  color: #fff;
  padding: 16px 48px;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 2px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-out 1s both;
  
  .btn-arrow {
    margin-left: 8px;
    transition: transform 0.3s ease;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    
    .btn-arrow {
      transform: translateX(4px);
    }
  }
}

// 动画关键帧
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes ringPulse {
  0% {
    transform: scale(0.95);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.5;
  }
}

@keyframes circleDraw {
  0% {
    stroke-dasharray: 0 283;
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dasharray: 283 283;
    stroke-dashoffset: 0;
  }
}

@keyframes checkDraw {
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes particleExplode {
  0% {
    opacity: 1;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
  100% {
    opacity: 0;
    transform: scale(0) rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .success-container {
    padding: 20px;
  }
  
  .content-wrapper {
    padding: 32px 20px;
  }
  
  .success-icon-wrapper {
    width: 140px;
    height: 140px;
    margin-bottom: 24px;
  }
  
  .title {
    font-size: 28px;
  }
  
  .subtitle {
    font-size: 16px;
    margin-bottom: 32px;
  }
  
  .start-btn {
    padding: 14px 36px;
    font-size: 16px;
  }
}
</style>