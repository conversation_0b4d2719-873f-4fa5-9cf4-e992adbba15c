<template>
  <div class="page">
    <div class="header">
      <span class="back-btn" @click="goBack">
        <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15 18l-6-6 6-6"/></svg>
      </span>
      <span class="header__title">{{ $t('reward.title') }}</span>
    </div>
    <div class="page__title">
      {{ $t('reward.subtitle') }}
    </div>
    <div class="card-list">
      <div class="card card--elevated">
        <div class="card__content">
          <div class="card__title">{{ $t('reward.totalIncentive') }}（USDT）</div>
          <div class="card__value">1000.32</div>
          <div class="card__desc">{{ $t('reward.totalIncentiveDesc') }}</div>
        </div>
        <span class="card__arrow">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="11"/><path d="M10 8l4 4-4 4"/></svg>
        </span>
      </div>
      <div class="card card--elevated">
        <div class="card__content">
          <div class="card__title">{{ $t('reward.level1Incentive') }}（USDT）</div>
          <div class="card__value">500.32</div>
          <div class="card__desc">{{ $t('reward.level1IncentiveDesc') }}</div>
        </div>
        <span class="card__arrow">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="11"/><path d="M10 8l4 4-4 4"/></svg>
        </span>
      </div>
      <div class="card card--elevated">
        <div class="card__content">
          <div class="card__title">{{ $t('reward.level2Incentive') }}（USDT）</div>
          <div class="card__value">300</div>
          <div class="card__desc">{{ $t('reward.level2IncentiveDesc') }}</div>
        </div>
      </div>
      <div class="card card--elevated">
        <div class="card__content">
          <div class="card__title">{{ $t('reward.level3Incentive') }}（USDT）</div>
          <div class="card__value">200</div>
          <div class="card__desc">{{ $t('reward.level3IncentiveDesc') }}</div>
        </div>
      </div>
    </div>
    <div class="section__title">{{ $t('reward.myBenefits') }}</div>
    <div class="benefit-row">
      <div class="benefit-block card card--elevated">
        <div class="benefit__label">{{ $t('reward.brokerLevel') }}</div>
        <div class="benefit__value">L1</div>
      </div>
      <div class="benefit-block card card--elevated">
        <div class="benefit__label">{{ $t('reward.level1Cashback') }}</div>
        <div class="benefit__value">0%</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()
function goBack() {
  router.back()
}
</script>

<style scoped lang="scss">
@import '../styles/variables';

.page {
  background: $secondary;
  min-height: 100vh;
  padding-bottom: $spacing-xl;
}

.header {
  display: flex;
  align-items: center;
  height: $spacing-2xl;
  padding: 0 0 0 $spacing-sm;
  background: $secondary;
  border-bottom: none;
  
  &__title {
    font-size: $font-size-base;
    color: $gray-400;
    font-weight: $font-weight-semibold;
    margin-left: $spacing-xs;
  }
}

.back-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-right: $spacing-xs;
  color: $gray-400;
  transition: color $transition-fast;
  
  &:hover {
    color: $gray-600;
  }
}

.page__title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
  margin: $spacing-md 0 0 $spacing-md;
  line-height: 1.3;
}

.card-list {
  margin: $spacing-md 0 0 0;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  padding: 0;
}

.card {
  background: $gray-50;
  border-radius: $radius-lg;
  margin: 0 $spacing-md;
  padding: $spacing-md;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: transform $transition-normal, box-shadow $transition-normal;
  
  &--elevated {
    box-shadow: $shadow-md;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-lg;
    }
  }
  
  &__content {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;
  }
  
  &__title {
    font-size: $font-size-sm;
    color: $gray-600;
    font-weight: $font-weight-medium;
  }
  
  &__value {
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $gray-900;
    margin: $spacing-xs 0;
  }
  
  &__desc {
    font-size: $font-size-xs;
    color: $gray-400;
    font-weight: $font-weight-normal;
  }
  
  &__arrow {
    margin-left: $spacing-sm;
    display: flex;
    align-items: center;
    color: $gray-900;
  }
}

.section__title {
  font-size: $font-size-base;
  font-weight: $font-weight-bold;
  color: $gray-600;
  margin: $spacing-lg 0 $spacing-sm $spacing-md;
}

.benefit-row {
  display: flex;
  gap: $spacing-sm;
  margin: 0 $spacing-md;
}

.benefit-block {
  flex: 1;
  padding: $spacing-md 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.benefit__label {
  font-size: $font-size-xs;
  color: $gray-600;
  font-weight: $font-weight-medium;
  margin-bottom: $spacing-sm;
}

.benefit__value {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
}

// 响应式设计
@media (max-width: $breakpoint-sm) {
  .card {
    padding: $spacing-sm;
    margin: 0 $spacing-sm;
    
    &__value {
      font-size: $font-size-xl;
    }
  }
  
  .card-list {
    gap: $spacing-sm;
  }
  
  .benefit-row {
    margin: 0 $spacing-sm;
  }
  
  .page__title {
    font-size: $font-size-lg;
    margin: $spacing-md 0 0 $spacing-sm;
  }
}

@media (min-width: $breakpoint-md) {
  .card-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-md;
    padding: 0 $spacing-md;
  }
  
  .card {
    margin: 0;
  }
}
</style>