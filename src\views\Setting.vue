<template>
  <div class="setting-page">
    <!-- 顶部导航栏 -->
    <div class="setting-header">
      <button class="back-btn" @click="$router.back()">
        <svg width="24" height="24" viewBox="0 0 24 24">
          <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      <h1>{{ $t('setting.title') }}</h1>
    </div>

    <!-- 设置列表 -->
    <div class="setting-list">
      <!-- 语言设置 -->
      <div class="setting-item" @click="showLanguageSelector = true">
        <div class="item-left">
          <span class="item-title">{{ $t('setting.language') }}</span>
        </div>
        <div class="item-right">
          <span class="item-value">{{ languageOptions[currentLanguage] }}</span>
          <svg class="arrow-icon" width="20" height="20" viewBox="0 0 24 24">
            <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>

      <!-- 计价货币 -->
      <div class="setting-item" @click="showCurrencySelector = true">
        <div class="item-left">
          <span class="item-title">{{ $t('setting.currency') }}</span>
        </div>
        <div class="item-right">
          <span class="item-value">{{ currencyOptions[currentCurrency] }}</span>
          <svg class="arrow-icon" width="20" height="20" viewBox="0 0 24 24">
            <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>

      <!-- 主题模式 -->
      <div class="setting-item">
        <div class="item-left">
          <span class="item-title">{{ $t('setting.theme') }}</span>
        </div>
        <div class="item-right">
          <ThemeSwitch />
        </div>
      </div>

      <!-- 颜色设置 -->
      <div class="setting-item" @click="showColorSelector = true">
        <div class="item-left">
          <span class="item-title">{{ $t('setting.color') }}</span>
        </div>
        <div class="item-right">
          <div class="color-indicators">
            <div class="color-up" :style="{ backgroundColor: upColor }"></div>
            <div class="color-down" :style="{ backgroundColor: downColor }"></div>
          </div>
          <svg class="arrow-icon" width="20" height="20" viewBox="0 0 24 24">
            <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>

      <!-- 涨跌幅周期和K线时间 -->
      <div class="setting-item" @click="showTimeSelector = true">
        <div class="item-left">
          <span class="item-title">{{ $t('setting.time') }}</span>
        </div>
        <div class="item-right">
          <span class="item-value">{{ timeZoneOptions[currentTimeZone] }}</span>
          <svg class="arrow-icon" width="20" height="20" viewBox="0 0 24 24">
            <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>

      <!-- 市场指标显示 -->
      <div class="setting-item" @click="showIndicatorSelector = true">
        <div class="item-left">
          <span class="item-title">{{ $t('setting.indicator') }}</span>
        </div>
        <div class="item-right">
          <span class="item-value">{{ indicatorOptions[currentIndicator] }}</span>
          <svg class="arrow-icon" width="20" height="20" viewBox="0 0 24 24">
            <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>

      <!-- 触感反馈 -->
      <div class="setting-item">
        <div class="item-left">
          <span class="item-title">{{ $t('setting.haptic') }}</span>
        </div>
        <div class="item-right">
          <label class="switch">
            <input type="checkbox" v-model="hapticFeedback" @change="updateHapticFeedback">
            <span class="slider"></span>
          </label>
        </div>
      </div>
    </div>

    <!-- 选择器弹窗 -->
    <Transition name="fade">
      <div class="selector-modal" v-if="showAnySelector" @click="closeAllSelectors">
        <div class="selector-content" @click.stop>
          <div class="selector-header">
            <h3>{{ getSelectorTitle() }}</h3>
            <button class="close-btn" @click="closeAllSelectors">
              <svg width="24" height="24" viewBox="0 0 24 24">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>
          
          <div class="selector-options">
            <template v-if="showLanguageSelector">
              <div 
                v-for="(name, code) in languageOptions" 
                :key="code"
                class="selector-option"
                :class="{ active: currentLanguage === code }"
                @click="selectLanguage(code)"
              >
                {{ name }}
                <svg v-if="currentLanguage === code" class="check-icon" width="20" height="20" viewBox="0 0 24 24">
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </template>

            <template v-if="showCurrencySelector">
              <div 
                v-for="(name, code) in currencyOptions" 
                :key="code"
                class="selector-option"
                :class="{ active: currentCurrency === code }"
                @click="selectCurrency(code)"
              >
                {{ name }}
                <svg v-if="currentCurrency === code" class="check-icon" width="20" height="20" viewBox="0 0 24 24">
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </template>

            <template v-if="showTimeSelector">
              <div 
                v-for="(name, code) in timeZoneOptions" 
                :key="code"
                class="selector-option"
                :class="{ active: currentTimeZone === code }"
                @click="selectTimeZone(code)"
              >
                {{ name }}
                <svg v-if="currentTimeZone === code" class="check-icon" width="20" height="20" viewBox="0 0 24 24">
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </template>

            <template v-if="showIndicatorSelector">
              <div 
                v-for="(name, code) in indicatorOptions" 
                :key="code"
                class="selector-option"
                :class="{ active: currentIndicator === code }"
                @click="selectIndicator(code)"
              >
                {{ name }}
                <svg v-if="currentIndicator === code" class="check-icon" width="20" height="20" viewBox="0 0 24 24">
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </template>

            <template v-if="showColorSelector">
              <div class="color-picker-section">
                <div class="color-picker-row">
                  <span>{{ $t('setting.upColor') }}</span>
                  <input type="color" v-model="upColor" @change="updateColors">
                </div>
                <div class="color-picker-row">
                  <span>{{ $t('setting.downColor') }}</span>
                  <input type="color" v-model="downColor" @change="updateColors">
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 切换用户按钮固定在底部 -->
    <div class="switch-user-bar">
      <button class="switch-user-btn" @click="switchUser">切换用户</button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import ThemeSwitch from '@/components/common/ThemeSwitch.vue'
import { getThemeSetting, THEME_TYPE } from '@/utils/theme'
import { useRouter } from 'vue-router'

const router = useRouter()

const { t, locale } = useI18n()

// 状态变量
const showLanguageSelector = ref(false)
const showCurrencySelector = ref(false)
const showColorSelector = ref(false)
const showTimeSelector = ref(false)
const showIndicatorSelector = ref(false)

const currentLanguage = ref('zh_CN')
const currentCurrency = ref('USD')
const currentTimeZone = ref('UTC+8')
const currentIndicator = ref('volume')
const hapticFeedback = ref(false)
const upColor = ref('#f44336')
const downColor = ref('#4caf50')

// 选项数据
const languageOptions = {
  zh_CN: '简体中文',
  zh_TW: '繁體中文',
  en_US: 'English',
  ko_KR: '한국어',
  ja_JP: '日本語'
}

const currencyOptions = {
  USD: '美元',
  CNY: '人民币',
  HKD: '港币',
  JPY: '日元',
  KRW: '韩元'
}

const timeZoneOptions = {
  'UTC+8': 'UTC+8',
  'UTC+9': 'UTC+9',
  'UTC+0': 'UTC+0',
  'UTC-4': 'UTC-4',
  'UTC-5': 'UTC-5'
}

const indicatorOptions = {
  volume: '成交量',
  macd: 'MACD',
  rsi: 'RSI',
  kdj: 'KDJ'
}

// 计算属性
const showAnySelector = computed(() => {
  return showLanguageSelector.value || 
         showCurrencySelector.value || 
         showColorSelector.value || 
         showTimeSelector.value || 
         showIndicatorSelector.value
})

// 方法
const getSelectorTitle = () => {
  if (showLanguageSelector.value) return t('setting.language')
  if (showCurrencySelector.value) return t('setting.currency')
  if (showColorSelector.value) return t('setting.color')
  if (showTimeSelector.value) return t('setting.time')
  if (showIndicatorSelector.value) return t('setting.indicator')
  return ''
}

const closeAllSelectors = () => {
  showLanguageSelector.value = false
  showCurrencySelector.value = false
  showColorSelector.value = false
  showTimeSelector.value = false
  showIndicatorSelector.value = false
}

const selectLanguage = (code) => {
  currentLanguage.value = code
  localStorage.setItem('language', code)
  locale.value = code
  closeAllSelectors()
  if (hapticFeedback.value) navigator.vibrate?.(50)
}

const selectCurrency = (code) => {
  currentCurrency.value = code
  localStorage.setItem('currency', code)
  closeAllSelectors()
  if (hapticFeedback.value) navigator.vibrate?.(50)
}

const selectTimeZone = (code) => {
  currentTimeZone.value = code
  localStorage.setItem('timezone', code)
  closeAllSelectors()
  if (hapticFeedback.value) navigator.vibrate?.(50)
}

const selectIndicator = (code) => {
  currentIndicator.value = code
  localStorage.setItem('indicator', code)
  closeAllSelectors()
  if (hapticFeedback.value) navigator.vibrate?.(50)
}

const updateColors = () => {
  localStorage.setItem('upColor', upColor.value)
  localStorage.setItem('downColor', downColor.value)
  if (hapticFeedback.value) navigator.vibrate?.(50)
}

const updateHapticFeedback = () => {
  localStorage.setItem('hapticFeedback', hapticFeedback.value.toString())
  if (hapticFeedback.value) navigator.vibrate?.(50)
}

function switchUser() {
  // 清理所有相关缓存
  localStorage.clear();
  // 跳转到登录页面
  router.replace('/login');
}

// 生命周期钩子
onMounted(() => {
  // 从本地存储加载设置
  currentLanguage.value = localStorage.getItem('language') || 'zh_CN'
  currentCurrency.value = localStorage.getItem('currency') || 'USD'
  currentTimeZone.value = localStorage.getItem('timezone') || 'UTC+8'
  currentIndicator.value = localStorage.getItem('indicator') || 'volume'
  hapticFeedback.value = localStorage.getItem('hapticFeedback') === 'true'
  upColor.value = localStorage.getItem('upColor') || '#f44336'
  downColor.value = localStorage.getItem('downColor') || '#4caf50'
})
</script>

<style scoped lang="scss">
.setting-page {
  min-height: 100vh;
  background-color: var(--background-color);
  padding-bottom: env(safe-area-inset-bottom);

  .setting-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: var(--card-background);
    padding: calc(env(safe-area-inset-top) + 12px) 16px 12px;
    display: flex;
    align-items: center;
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);

    h1 {
      margin: 0 auto;
      font-size: 18px;
      font-weight: 600;
    }

    .back-btn {
      background: none;
      border: none;
      padding: 8px;
      margin-left: -8px;
      border-radius: 50%;
      color: var(--text-color);
      cursor: pointer;
      
      &:active {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
  }

  .setting-list {
    padding: 16px;
  }

  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: var(--card-background);
    border-radius: 12px;
    margin-bottom: 12px;
    cursor: pointer;
    
    &:active {
      opacity: 0.8;
    }

    .item-left {
      .item-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-color);
      }
    }

    .item-right {
      display: flex;
      align-items: center;
      
      .item-value {
        font-size: 14px;
        color: var(--text-secondary);
        margin-right: 8px;
      }

      .arrow-icon {
        color: var(--text-tertiary);
      }

      .color-indicators {
        display: flex;
        margin-right: 8px;
        
        .color-up, .color-down {
          width: 16px;
          height: 16px;
          border-radius: 4px;
          margin-right: 4px;
        }
      }
    }
  }

  // 开关样式
  .switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 28px;
    
    input {
      opacity: 0;
      width: 0;
      height: 0;
      
      &:checked + .slider {
        background-color: var(--primary-color);
      }
      
      &:checked + .slider:before {
        transform: translateX(22px);
      }
    }
    
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 34px;
      
      &:before {
        position: absolute;
        content: "";
        height: 20px;
        width: 20px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
      }
    }
  }

  // 选择器弹窗
  .selector-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10001;
    display: flex;
    align-items: flex-end;
    
    .selector-content {
      width: 100%;
      background-color: var(--card-background);
      border-radius: 20px 20px 0 0;
      padding: 20px 0;
      max-height: 70vh;
      overflow-y: auto;
      
      .selector-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px 16px;
        border-bottom: 1px solid var(--border-color);
        
        h3 {
          font-size: 18px;
          font-weight: 600;
          margin: 0;
        }
        
        .close-btn {
          background: none;
          border: none;
          padding: 8px;
          color: var(--text-color);
          cursor: pointer;
          border-radius: 50%;
          
          &:active {
            background-color: rgba(0, 0, 0, 0.05);
          }
        }
      }
      
      .selector-options {
        padding: 8px 0;
        
        .selector-option {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px;
          cursor: pointer;
          
          &:active {
            background-color: rgba(0, 0, 0, 0.05);
          }
          
          &.active {
            color: var(--primary-color);
          }
        }
        
        .color-picker-section {
          padding: 16px 20px;
          
          .color-picker-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            span {
              font-size: 16px;
            }
            
            input[type="color"] {
              width: 40px;
              height: 40px;
              border: none;
              border-radius: 8px;
              cursor: pointer;
            }
          }
        }
      }
    }
  }

  // 过渡动画
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
}

.switch-user-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16px 16px calc(env(safe-area-inset-bottom, 0px) + 16px) 16px;
  background: var(--card-background, #fff);
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
  z-index: 9999;
  display: flex;
  justify-content: center;
}
.switch-user-btn {
  width: 100%;
  max-width: 420px;
  background: var(--primary-color, #1bc47d);
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  padding: 14px 0;
  box-shadow: 0 2px 8px rgba(27, 196, 125, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: #18b06f;
    box-shadow: 0 4px 12px rgba(27, 196, 125, 0.3);
    transform: translateY(-1px);
  }
  
  &:active {
    background: #159a60;
    transform: translateY(0);
  }
}
</style>