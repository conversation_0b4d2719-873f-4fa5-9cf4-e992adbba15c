<template>
  <div class="trade-page">
    <!-- 顶部用户栏 -->
    <div class="user-bar">
      <img class="avatar" src="https://randomuser.me/api/portraits/men/32.jpg" alt="avatar" />
      <div class="user-info">
        <div class="user-name">{{ $t('common.mockUsers.daoren') }}</div>
        <div class="user-type">{{ $t('trade.userType') }}</div>
      </div>
      <div class="user-balance">
        <div class="balance">$ {{ accountType === $t('trade.accountType.mock') ? mockBalance : realBalance }}</div>
        <div class="account-type-row" @click="showAccountDropdown = !showAccountDropdown" style="cursor:pointer;">
          <span class="account-type">{{ accountType }}</span>
          <svg class="account-arrow" width="18" height="18" viewBox="0 0 24 24" style="vertical-align:middle;margin-left:2px;"><path d="M7 10l5 5 5-5" stroke="#222" stroke-width="2" fill="none" stroke-linecap="round"/></svg>
        </div>
        <div v-if="showAccountDropdown" class="account-dropdown-mask" @click.self="showAccountDropdown = false">
          <div class="account-dropdown-list">
            <div v-for="type in accountTypes" :key="type" class="account-dropdown-item" :class="{selected: accountType === type}" @click.stop="selectAccountType(type)">{{ type }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 交易对信息 -->
    <div class="pair-bar enhanced-pair-bar">
      <div class="pair-title-group" style="position:relative;">
        <div class="pair-select-trigger" @click="showPairDropdown = !showPairDropdown" style="display:flex;align-items:center;cursor:pointer;">
          <span class="pair-title">{{ pairs.find(p=>p.value===selectedPair)?.label }}</span>
          <svg class="pair-arrow" width="14" height="14" viewBox="0 0 24 24" style="margin-left:2px;margin-right:6px;vertical-align:middle;"><path d="M7 10l5 5 5-5" stroke="#222" stroke-width="2" fill="none" stroke-linecap="round"/></svg>
        </div>
        <span class="pair-rate" :class="{up: pairUp, down: !pairUp}">{{ pairChange > 0 ? '+' : '' }}{{ pairChange }}%</span>
        <div v-if="showPairDropdown" class="pair-dropdown-list">
          <div v-for="pair in pairs" :key="pair.value" class="pair-dropdown-item" :class="{selected: pair.value===selectedPair}" @click.stop="selectPair(pair.value)">
            {{ pair.label }}
          </div>
        </div>
      </div>
      <div class="pair-icons">
        <svg width="24" height="24" viewBox="0 0 24 24" @click="showIndicatorDropdown = !showIndicatorDropdown" style="cursor:pointer;">
          <polyline points="3,17 8,12 13,16 17,10 21,14" fill="none" stroke="#111" stroke-width="2"/>
          <rect x="2" y="3" width="20" height="18" fill="none" stroke="#111" stroke-width="1.5"/>
        </svg>
        <svg width="24" height="24" viewBox="0 0 24 24"><circle cx="6" cy="12" r="1.5" fill="#111"/><circle cx="12" cy="12" r="1.5" fill="#111"/><circle cx="18" cy="12" r="1.5" fill="#111"/></svg>
        <div v-if="showIndicatorDropdown" class="indicator-dropdown-mask" @click.self="showIndicatorDropdown = false">
          <div class="indicator-dropdown-list">
            <div v-for="ind in indicators" :key="ind" class="indicator-dropdown-item" :class="{selected: selectedIndicator === ind}" @click.stop="selectedIndicator = ind; showIndicatorDropdown = false">
              {{ ind }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 分时图时间周期选项 -->
    <div class="chart-tabs-bar">
      <span v-for="tab in chartTabs" :key="tab" :class="['chart-tab', {active: tab === selectedChartTab}]" @click="selectedChartTab = tab">{{ tab }}</span>
    </div>
    <!-- 图表区 -->
    <div class="chart-area enhanced-chart-area">
      <div class="chart-mock">
        <!-- MA 均线标签浮于分时图左上角 -->
        <div class="chart-ma-overlay">
          <span class="ma-item" v-for="ma in maList" :key="ma.label" :style="{color: ma.color}">{{ ma.label }}: {{ ma.value }}</span>
        </div>
        <v-chart :option="chartOption" autoresize style="width:100%;height:260px;" />
      </div>
      <div class="chart-action-icons chart-action-icons-below">
        <button class="chart-btn chart-btn-search">
          <!-- 闹钟图标 -->
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="13" r="6" stroke="#fff" stroke-width="2"/>
            <path d="M12 13V10" stroke="#fff" stroke-width="2" stroke-linecap="round"/>
            <path d="M12 13L14 15" stroke="#fff" stroke-width="2" stroke-linecap="round"/>
            <path d="M5.5 7L4 5.5" stroke="#fff" stroke-width="2" stroke-linecap="round"/>
            <path d="M18.5 7L20 5.5" stroke="#fff" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </button>
        <button class="chart-btn chart-btn-order">
          <!-- 旗帜图标 -->
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 20V5M6 5H18L16 9L18 13H6" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
    <!-- 操作区 -->
    <div class="trade-ops">
      <div class="trade-info-card">
        <div class="trade-info-row">
          <div class="trade-info-col">
            <div class="trade-info-label">{{ $t('trade.tradeInfo.time') }}</div>
            <div class="trade-info-input" @click="showTimeDropdown = true" style="cursor:pointer;position:relative;">
              {{ selectedTime.label }}
              <svg class="icon-clock" width="16" height="16" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="#bbb" stroke-width="2" fill="none"/><path d="M12 7v5l4 2" stroke="#bbb" stroke-width="2" fill="none" stroke-linecap="round"/></svg>
            </div>
            <div v-if="showTimeDropdown" class="time-dropdown-mask" @click.self="showTimeDropdown = false">
              <div class="time-dropdown-grid">
                <div v-for="t in timeOptions" :key="t.value" class="time-dropdown-item" :class="{selected: t.value===selectedTime.value}" @click.stop="selectTime(t)">
                  <svg width="48" height="48" viewBox="0 0 48 48" class="time-progress">
                    <circle cx="24" cy="24" r="22" fill="none"/>
                    <circle cx="24" cy="24" r="22" fill="none" :stroke-dasharray="138.16" :stroke-dashoffset="getProgressOffset(t)" stroke-linecap="round"/>
                    <text x="24" y="28" text-anchor="middle" font-weight="600">{{ t.label }}</text>
                  </svg>
                  <div class="time-label">{{ getFutureTime(t.value) }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="trade-info-col">
            <div class="trade-info-label">{{ $t('trade.tradeInfo.amount') }}</div>
            <div class="trade-info-input" style="padding:0;">
              <input v-model="amount" type="number" min="1" class="amount-input" style="border:none;background:transparent;width:70px;padding:8px 0 8px 12px;font-size:14px;font-weight:bold;outline:none;" />
              <span style="margin-left:4px;">$</span>
            </div>
          </div>
        </div>
        <div class="trade-info-row trade-info-result">
          <div class="trade-info-result-col">
            <div class="trade-info-result-label">{{ $t('trade.tradeInfo.return') }}</div>
            <div class="trade-info-result-value">${{ (amount * 1.75).toFixed(2) }}</div>
          </div>
          <div class="trade-info-result-col">
            <div class="trade-info-result-label"></div>
            <div class="trade-info-result-value" style="color: #2ecc71;">+75%</div>
          </div>
          <div class="trade-info-result-col">
            <div class="trade-info-result-label">{{ $t('trade.tradeInfo.profit') }}</div>
            <div class="trade-info-result-value">+${{ (amount * 0.75).toFixed(2) }}</div>
          </div>
        </div>
      </div>
      <div class="ops-btns">
        <button class="btn-rise" :class="{active: lastAction==='rise'}" @click="handleTrade('rise')">
          <span class="btn-icon">
            <svg width="28" height="28" viewBox="0 0 28 28"><circle cx="14" cy="14" r="14" fill="#fff" fill-opacity="0.3"/><path d="M9 17l5-5 5 5" stroke="#fff" stroke-width="2" fill="none" stroke-linecap="round"/></svg>
          </span>
          {{ $t('trade.buttons.rise') }}
        </button>
        <button class="btn-fall" :class="{active: lastAction==='fall'}" @click="handleTrade('fall')">
          <span class="btn-icon">
            <svg width="28" height="28" viewBox="0 0 28 28"><circle cx="14" cy="14" r="14" fill="#fff" fill-opacity="0.3"/><path d="M9 11l5 5 5-5" stroke="#fff" stroke-width="2" fill="none" stroke-linecap="round"/></svg>
          </span>
          {{ $t('trade.buttons.fall') }}
        </button>
      </div>
    </div>
    <!-- 订单列表 -->
    <div class="order-list-area">
      <div class="order-tabs">
        <span :class="{active: orderTab==='unfinished'}" @click="orderTab='unfinished'">{{ $t('trade.orders.unfinished') }} ({{ unfinishedOrders.length }})</span>
        <span :class="{active: orderTab==='finished'}" @click="orderTab='finished'">{{ $t('trade.orders.finished') }} ({{ finishedOrders.length }})</span>
      </div>
      <template v-if="orderTab==='unfinished'">
        <div class="order-header-row">
          <span class="order-header order-header-asset">{{ $t('trade.orders.asset') }}</span>
          <span class="order-header order-header-remain">{{ $t('trade.orders.remainTime') }}</span>
          <span class="order-header order-header-buy">{{ $t('trade.orders.buyIndex') }}</span>
          <span class="order-header order-header-current">{{ $t('trade.orders.currentIndex') }}</span>
          <span class="order-header order-header-profit">{{ $t('trade.orders.expectedProfit') }}</span>
        </div>
        <div class="order-list">
          <div class="order-item" v-for="order in unfinishedOrders" :key="order.id">
            <div class="order-asset">{{ order.asset }}</div>
            <div class="order-remain">{{ order.remainTime }}</div>
            <div class="order-buy">{{ order.buyIndex }}</div>
            <div class="order-current">{{ order.currentIndex }}</div>
            <div class="order-profit" :style="{color: order.profit > 0 ? '#f66' : (order.profit < 0 ? '#1bc47d' : '#888')}" style="font-weight:bold;">
              {{ order.profit > 0 ? '+' : '' }}{{ order.profit }}
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="order-header-row">
          <span class="order-header order-header-date">{{ $t('trade.orders.date') }}</span>
          <span class="order-header order-header-asset">{{ $t('trade.orders.asset') }}</span>
          <span class="order-header order-header-end">{{ $t('trade.orders.endTime') }}</span>
          <span class="order-header order-header-profit">{{ $t('trade.orders.rate') }}</span>
        </div>
        <div class="order-list">
          <div class="order-item" v-for="order in finishedOrders" :key="order.id">
            <div class="order-date">{{ order.date }}</div>
            <div class="order-asset">
              <div style="margin-bottom:2px;">{{ order.asset }}</div>
              <div style="color:#f6a700;font-size:12px;line-height:1.2;">
                ${{ order.amount }}<span style="color:#f66;font-size:11px;">*{{ order.multiple }}</span>
              </div>
            </div>
            <div class="order-end">{{ order.endTime }}</div>
            <div class="order-profit">
              <div style="display:flex;align-items:center;justify-content:flex-end;gap:4px;">
                <svg v-if="order.profit > 0" width="10" height="10" viewBox="0 0 12 12"><polygon points="6,2 10,8 2,8" fill="#1bc47d"/></svg>
                <svg v-else width="10" height="10" viewBox="0 0 12 12"><polygon points="6,10 2,4 10,4" fill="#f66"/></svg>
                <span :style="{color: order.profit > 0 ? '#1bc47d' : '#f66', fontWeight: 'bold'}">
                  {{ order.profit > 0 ? '+' : '' }}${{ order.profit.toFixed(2) }}
                </span>
              </div>
              <div :style="{color: order.profit > 0 ? '#1bc47d' : '#f66', fontSize: '11px', textAlign: 'right'}">
                {{ order.profit > 0 ? '+' : '' }}{{ order.rate }}%
              </div>
            </div>
          </div>
        </div>
      </template>
      
      <TabBar />
    </div>
  </div>
</template>

<script setup>
import TabBar from '../components/TabBar.vue'
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const orderTab = ref('unfinished')
const unfinishedOrders = ref([
  { id: 1, asset: 'BTC/OTC', remainTime: '00:05:00', buyIndex: '6440.12', currentIndex: '6440.83', profit: 100.24 },
  { id: 2, asset: 'ETH/OTC', remainTime: '00:00:30', buyIndex: '64398.11', currentIndex: '64398.01', profit: -10.28 },
  { id: 3, asset: 'BTC/OTC', remainTime: '00:00:15', buyIndex: '639.1234', currentIndex: '639.1333', profit: 1000.12 },
  { id: 4, asset: 'BTC/OTC', remainTime: '00:10:00', buyIndex: '76899.33', currentIndex: '76899.56', profit: 9931.2 },
])
const finishedOrders = ref([
  { id: 1, date: '2025-05-10', asset: 'BTC/OTC', amount: 100, multiple: 10, endTime: '14:43:30', profit: 7.5, rate: 75 },
  { id: 2, date: '2025-05-10', asset: 'BTC/OTC', amount: 100, multiple: 20, endTime: '14:43:30', profit: -10, rate: -100 },
])

// 账户类型切换
const accountType = ref(t('trade.accountType.mock'))
const mockBalance = 9871.00
const realBalance = 12345.67

// 币种及涨幅
const pairUp = ref(true)
const pairChange = ref(0.38) // 正数为涨，负数为跌

// 指标切换
const indicators = ['MA5', 'MA10', 'MA20', 'EMA', 'BOLL']
const selectedIndicator = ref(indicators[0])

// 产品/交易对列表
const pairs = [
  { value: 'BTC', label: 'BTC/OTC' },
  { value: 'ETH', label: 'ETH/OTC' },
  { value: 'LINK', label: 'Chainlink/OTC' },
  { value: 'AMZN', label: 'Amazon/OTC' },
  { value: 'MCD', label: 'McDonald/OTC' },
]
const selectedPair = ref('BTC')
const showPairDropdown = ref(false)
function selectPair(val) {
  selectedPair.value = val
  showPairDropdown.value = false
}

// 图表相关
const chartTabs = ['1D', '3H', '1H', '30m', '15m', '10m']
const selectedChartTab = ref('1H')

// 时间选择相关
const timeOptions = [
  { value: 30, label: t('trade.timeOptions.30s') },
  { value: 60, label: t('trade.timeOptions.1m') },
  { value: 300, label: t('trade.timeOptions.5m') },
  { value: 900, label: t('trade.timeOptions.15m') },
  { value: 1800, label: t('trade.timeOptions.30m') },
  { value: 3600, label: t('trade.timeOptions.1h') },
]
const selectedTime = ref(timeOptions[1])
const showTimeDropdown = ref(false)

// 交易相关
const amount = ref(100)
const lastAction = ref('')

// 生成平滑的随机分时数据
function generateRandomChartData() {
  const basePrice = 6440.00
  const data = []
  
  // 计算第一根虚线对应的数据点位置（大约在第30个点左右）
  const firstLinePosition = 30
  
  // 生成前半段上升趋势的控制点
  const upKeyPoints = []
  for (let i = 0; i < 4; i++) {
    upKeyPoints.push(basePrice - 0.15 + (i * 0.1) + (Math.random() - 0.5) * 0.05)
  }
  
  // 生成后半段下降趋势的控制点
  const downKeyPoints = []
  for (let i = 0; i < 4; i++) {
    downKeyPoints.push(basePrice + 0.15 - (i * 0.1) + (Math.random() - 0.5) * 0.05)
  }
  
  // 生成前半段数据（上升趋势）
  for (let i = 0; i < firstLinePosition; i++) {
    const t = i / (firstLinePosition - 1)
    const segment = Math.floor(t * (upKeyPoints.length - 1))
    const localT = (t * (upKeyPoints.length - 1)) - segment
    
    let price
    if (segment < upKeyPoints.length - 1) {
      const p0 = upKeyPoints[Math.max(0, segment - 1)]
      const p1 = upKeyPoints[segment]
      const p2 = upKeyPoints[segment + 1]
      const p3 = upKeyPoints[Math.min(upKeyPoints.length - 1, segment + 2)]
      
      const t2 = localT * localT
      const t3 = t2 * localT
      
      price = 0.5 * (
        (-p0 + 3*p1 - 3*p2 + p3) * t3 +
        (2*p0 - 5*p1 + 4*p2 - p3) * t2 +
        (-p0 + p2) * localT +
        (2*p1)
      )
    } else {
      price = upKeyPoints[upKeyPoints.length - 1]
    }
    
    const noise = (Math.random() - 0.5) * 0.05
    price += noise
    price = Math.max(6439.90, Math.min(6440.30, price))
    
    data.push(parseFloat(price.toFixed(2)))
  }
  
  // 生成后半段数据（下降趋势）
  for (let i = firstLinePosition; i < 60; i++) {
    const t = (i - firstLinePosition) / (59 - firstLinePosition)
    const segment = Math.floor(t * (downKeyPoints.length - 1))
    const localT = (t * (downKeyPoints.length - 1)) - segment
    
    let price
    if (segment < downKeyPoints.length - 1) {
      const p0 = downKeyPoints[Math.max(0, segment - 1)]
      const p1 = downKeyPoints[segment]
      const p2 = downKeyPoints[segment + 1]
      const p3 = downKeyPoints[Math.min(downKeyPoints.length - 1, segment + 2)]
      
      const t2 = localT * localT
      const t3 = t2 * localT
      
      price = 0.5 * (
        (-p0 + 3*p1 - 3*p2 + p3) * t3 +
        (2*p0 - 5*p1 + 4*p2 - p3) * t2 +
        (-p0 + p2) * localT +
        (2*p1)
      )
    } else {
      price = downKeyPoints[downKeyPoints.length - 1]
    }
    
    const noise = (Math.random() - 0.5) * 0.05
    price += noise
    price = Math.max(6439.90, Math.min(6440.30, price))
    
    data.push(parseFloat(price.toFixed(2)))
  }
  
  return data
}

// 生成随机均线数据
function generateRandomMAData() {
  const basePrice = 6440.00
  return [
    { label: 'MA5', value: (basePrice + (Math.random() - 0.5) * 0.3).toFixed(2), color: '#f6a700' },
    { label: 'MA10', value: (basePrice + (Math.random() - 0.5) * 0.3).toFixed(2), color: '#e67e22' },
    { label: 'MA20', value: (basePrice + (Math.random() - 0.5) * 0.3).toFixed(2), color: '#3498db' },
  ]
}

// 产品信息区模拟均线数据
const maList = ref(generateRandomMAData())
// 生成随机时间轴数据
function generateTimeAxisData() {
  const data = []
  const now = new Date()
  now.setMinutes(now.getMinutes() - 10) // 从10分钟前开始
  
  for (let i = 0; i < 60; i++) {
    const time = new Date(now.getTime() + i * 10000) // 每10秒一个点
    data.push(time.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    }))
  }
  
  return data
}

// 生成随机图表数据
const chartData = ref(generateRandomChartData())
const timeAxisData = ref(generateTimeAxisData())

// vue-echarts 图表配置，严格仿照截图
const chartOption = computed(() => ({
  grid: { left: 24, right: 2, top: 10, bottom: 25, containLabel: false },
  backgroundColor: '#fff',
  xAxis: {
    type: 'category',
    data: timeAxisData.value,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: {
      color: '#bbb',
      fontSize: 10,
      fontWeight: 500,
      margin: 16,
      align: 'center',
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: '#f3f3f3',
        width: 1,
        type: [4, 4]
      }
    },
    boundaryGap: true,
    z: 10
  },
  yAxis: {
    type: 'value',
    min: 6439.90,
    max: 6440.30,
    splitNumber: 8,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: { 
      color: '#bbb', 
      fontSize: 10, 
      fontWeight: 700, 
      align: 'right', 
      margin: 2,
      formatter: function(value) {
        return value.toFixed(2);
      }
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: '#f3f3f3',
        width: 1,
        type: [4, 4]
      }
    },
    z: 10,
    position: 'right',
    scale: true, // 启用缩放，让数据更平滑
  },
  series: [
    {
      type: 'line',
      data: chartData.value,
      lineStyle: { color: '#b2e672', width: 2.5, shadowBlur: 3, shadowColor: 'rgba(178,230,114,0.3)' },
      symbol: 'none',
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(178,230,114,0.15)' },
            { offset: 0.5, color: 'rgba(178,230,114,0.08)' },
            { offset: 1, color: 'rgba(178,230,114,0.01)' }
          ]
        }
      },
      smooth: true,
      smoothMonotone: 'x', // 确保平滑曲线单调性
      smoothConstraint: true, // 启用平滑约束
      sampling: 'lttb', // 使用 LTTB 采样算法
      z: 3
    },
    // 只显示最后一个点
    {
      type: 'scatter',
      data: [[chartData.value.length - 1, chartData.value[chartData.value.length - 1]]],
      symbolSize: 13,
      itemStyle: { color: '#b2e672', borderColor: '#fff', borderWidth: 5 },
      z: 4
    },
    // 红色虚线（当前价）
    {
      type: 'line',
      data: (() => {
        const data = new Array(chartData.value.length).fill(null)
        // 只显示到第1个点，后面的连线不显示
        for (let i = 0; i <= 1; i++) {
          data[i] = chartData.value[i]
        }
        return data
      })(),
      lineStyle: { color: '#f66', width: 1, type: 'dashed' },
      symbol: 'none',
      z: 4
    },
    // 当前价红色标注
    {
      type: 'scatter',
      data: [[1, chartData.value[1]]],
      symbolSize: 14,
      itemStyle: { color: '#6dd400', borderColor: '#fff', borderWidth: 4 },
      z: 5,
      label: {
        show: true,
        formatter: chartData.value[1].toString(),
        position: [20, 0],
        color: '#f66',
        fontSize: 12,
        fontWeight: 700,
        borderRadius: 4,
        padding: [0, 0, 0, 0],
        backgroundColor: 'transparent',
      }
    },
    // 最高点标注
    {
      type: 'scatter',
      data: (() => {
        const maxIndex = chartData.value.indexOf(Math.max(...chartData.value))
        return [[maxIndex, chartData.value[maxIndex]]]
      })(),
      symbolSize: 10,
      itemStyle: { color: '#b2e672', borderColor: '#fff', borderWidth: 2 },
      label: {
        show: true,
        formatter: `最大 ${Math.max(...chartData.value).toFixed(2)}`,
        position: 'top',
        color: '#888',
        fontSize: 11,
        fontWeight: 700,
        offset: [0, -12]
      },
      z: 5
    },
    // 最低点标注
    {
      type: 'scatter',
      data: (() => {
        const minIndex = chartData.value.indexOf(Math.min(...chartData.value))
        return [[minIndex, chartData.value[minIndex]]]
      })(),
      symbolSize: 10,
      itemStyle: { color: '#b2e672', borderColor: '#fff', borderWidth: 2 },
      label: {
        show: true,
        formatter: `最小 ${Math.min(...chartData.value).toFixed(2)}`,
        position: 'bottom',
        color: '#888',
        fontSize: 11,
        fontWeight: 700,
        offset: [0, 0]
      },
      z: 5
    }
  ],
  graphic: [
    // 左侧量柱，zIndex 提高，left/top 绝对定位悬浮
    {
      type: 'rect',
      left: 8, top: 0, z: 100,
      shape: { width: 18, height: 240 },
      style: {
        fill: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(246,102,102,0.95)' },
            { offset: 0.4, color: 'rgba(246,102,102,0.7)' },
            { offset: 0.4, color: '#e0e0e0' },
            { offset: 1, color: 'rgba(27,196,125,0.7)' }
          ]
        },
        shadowBlur: 0
      }
    },
    // 40%
    {
      type: 'text',
      left: 6, top: 10, z: 101,
      style: { text: '40%', fill: '#666', font: '10px sans-serif', textAlign: 'center' },
    },
    // 卖出
    {
      type: 'text',
      left: 6, top: 25, z: 101,
      style: { text: '卖出', fill: '#666', font: '10px sans-serif', textAlign: 'center' },
    },
    // 60%
    {
      type: 'text',
      left: 6, top: 215, z: 101,
      style: { text: '60%', fill: '#666', font: '10px sans-serif', textAlign: 'center' },
    },
    // 购买
    {
      type: 'text',
      left: 6, top: 200, z: 101,
      style: { text: '购买', fill: '#666', font: '10px sans-serif', textAlign: 'center' },
    },
    // 顶部分隔线
    {
      type: 'rect',
      left: 0, top: 0, z: 20,
      shape: { width: 1000, height: 1 },
      style: { fill: '#ededed' }
    },
    // 右侧两根纵向虚线
    {
      type: 'line',
      shape: { x1: 300, y1: 0, x2: 300, y2: 292 },
      style: { stroke: '#bbb', lineWidth: 1, lineDash: [4, 4] },
      z: 50
    },
    {
      type: 'line',
      shape: { x1: 343, y1: 0, x2: 343, y2: 292 },
      style: { stroke: '#111', lineWidth: 1, lineDash: [4, 4] },
      z: 50
    }
  ]
}))

// 方法
function selectTime(time) {
  selectedTime.value = time
  showTimeDropdown.value = false
}

function getProgressOffset(time) {
  const now = new Date()
  const future = new Date(now.getTime() + time.value * 1000)
  const progress = (future - now) / (time.value * 1000)
  return 138.16 * (1 - progress)
}

function getFutureTime(seconds) {
  const now = new Date()
  const future = new Date(now.getTime() + seconds * 1000)
  return future.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', second: '2-digit' })
}

function handleTrade(action) {
  lastAction.value = action
  // 这里添加实际的交易逻辑
  console.log(`${action} trade with amount: ${amount.value}`)
}

// 组件挂载时启动自动刷新
onMounted(() => {
  // 每15秒自动刷新一次数据，让曲线更流畅
  // autoRefreshTimer = setInterval(() => {
  //   refreshChartData()
  // }, 15000)
})

// 组件卸载时清除定时器
onUnmounted(() => {
  // if (autoRefreshTimer) {
  //   clearInterval(autoRefreshTimer)
  //   autoRefreshTimer = null
  // }
})

const accountTypes = [t('trade.accountType.mock'), t('trade.accountType.real')]
const showAccountDropdown = ref(false)
function selectAccountType(type) {
  accountType.value = type
  showAccountDropdown.value = false
}

const showIndicatorDropdown = ref(false)
</script>

<style scoped lang="scss">
.trade-page {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
  padding-bottom: 60px;
}
.user-bar {
  display: flex;
  align-items: center;
  padding: 18px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  
  .avatar {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    margin-right: 14px;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .user-info {
    flex: 1;
    .user-name {
      font-size: 16px;
      font-weight: 600;
      color: #222;
      margin-bottom: 2px;
    }
    .user-type {
      font-size: 13px;
      color: #666;
      background: rgba(27, 196, 125, 0.1);
      color: #1bc47d;
      padding: 2px 8px;
      border-radius: 12px;
      display: inline-block;
      font-weight: 500;
    }
  }
  
  .user-balance {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 110px;
    .balance {
      color: #f6a700;
      font-weight: 700;
      font-size: 20px;
      margin-bottom: 2px;
      display: block;
      text-align: center;
    }
    .account-type-row {
      display: flex;
      align-items: center;
      justify-content: center;
      .account-type {
        color: #222;
        font-size: 15px;
        font-weight: 400;
        margin-right: 2px;
      }
      .account-arrow {
        width: 18px;
        height: 18px;
        display: inline-block;
      }
    }
  }
}
.pair-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0 0 0;
  background: #fff;
  margin-left: 10px;
  border-bottom: none;
  box-shadow: none;
  min-height: 38px;
  .pair-title-group {
    display: flex;
    align-items: center;
    .pair-title {
      font-size: 14px;
      font-weight: 700;
      color: #222;
      letter-spacing: 0.2px;
    }
    .pair-arrow {
      margin-left: 2px;
      margin-right: 6px;
      vertical-align: middle;
    }
    .pair-rate {
      font-size: 15px;
      font-weight: 600;
      color: #1bc47d;
      margin-left: 0;
      vertical-align: middle;
    }
  }
  .pair-icons {
    display: flex;
    align-items: center;
    gap: 8px;
    svg {
      width: 22px;
      height: 22px;
      padding: 0;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      &:hover {
        background: rgba(0, 0, 0, 0.04);
        transform: scale(1.05);
      }
      &:active {
        transform: scale(0.95);
      }
    }
  }
}
.chart-area {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  
  .chart-tabs {
    display: flex;
    gap: 0;
    padding: 0 16px;
    border-bottom: 1px solid #f0f0f0;
    
    span {
      flex: 1;
      text-align: center;
      font-size: 14px;
      color: #888;
      cursor: pointer;
      padding: 12px 8px;
      font-weight: 500;
      transition: all 0.2s ease;
      position: relative;
      
      &:hover {
        color: #666;
        background: rgba(0, 0, 0, 0.02);
      }
      
      &.active {
        color: #222;
        font-weight: 600;
        background: rgba(27, 196, 125, 0.05);
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40px;
          height: 3px;
          background: #1bc47d;
          border-radius: 2px 2px 0 0;
        }
      }
    }
  }
  
  .chart-mock {
    position: relative;
    width: 100%;
    height: 260px;
    background: none !important;
    border-radius: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    overflow: hidden;
  }
  .chart-ma-overlay {
    position: absolute;
    left: 0;
    right: 0;
    top: 10px;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    font-size: 12px;
    font-weight: normal;
    text-shadow: 0 1px 2px #fff8;
    white-space: nowrap;
  }
  .chart-balance-overlay {
    position: absolute;
    top: 0;
    right: 32px;
    z-index: 20;
    color: #f6a700;
    font-size: 20px;
    font-weight: bold;
    text-align: right;
    letter-spacing: 1px;
    pointer-events: none;
    background: transparent;
  }
}
.trade-ops {
  padding: 12px;
  .trade-info-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 12px;
    .trade-info-row {
      display: flex;
      gap: 12px;
      margin-bottom: 10px;
      &:last-child {
        margin-bottom: 0;
      }
      .trade-info-col {
        flex: 1;
        .trade-info-label {
          font-size: 12px;
          color: #888;
          margin-bottom: 3px;
        }
        .trade-info-input {
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: #fff;
          padding: 6px 10px;
          border-radius: 6px;
          font-size: 13px;
          font-weight: bold;
        }
      }
      &.trade-info-result {
        .trade-info-result-col {
          flex: 1;
          text-align: center;
          .trade-info-result-label {
            font-size: 11px;
            color: #888;
            margin-bottom: 2px;
          }
          .trade-info-result-value {
            font-size: 14px;
            font-weight: bold;
          }
        }
      }
    }
  }
  .ops-btns {
    display: flex;
    gap: 10px;
    .btn-rise, .btn-fall {
      flex: 1;
      height: 48px;
      border: none;
      border-radius: 10px;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      transition: all 0.2s;
      &:active {
        transform: scale(0.98);
      }
    }
    .btn-rise {
      background: linear-gradient(135deg, #1bc47d, #16a085);
      color: #fff;
    }
    .btn-fall {
      background: linear-gradient(135deg, #f66, #e74c3c);
      color: #fff;
    }
  }
}
.order-list-area {
  flex: 1;
  padding: 12px;
  .order-tabs {
    display: flex;
    gap: 0;
    margin-bottom: 12px;
    span {
      flex: 1;
      text-align: center;
      font-size: 14px;
      color: #bbb;
      background: none;
      border: none;
      padding: 8px 0 6px;
      font-weight: 400;
      cursor: pointer;
      transition: all 0.2s;
      border-radius: 0;
      &.active {
        color: #111;
        font-weight: 600;
        background: none;
        border-bottom: 2px solid #111;
      }
    }
  }
  .order-header-row {
    display: grid;
    grid-template-columns: 1.5fr 1.5fr 1fr 1fr;
    gap: 6px;
    padding: 6px 0;
    border-bottom: 1px solid #f2f2f2;
    font-size: 11px;
    color: #888;
    font-weight: bold;
  }
  .order-list {
    .order-item {
      display: grid;
      grid-template-columns: 1.5fr 1.5fr 1fr 1fr;
      gap: 6px;
      padding: 8px 0;
      border-bottom: 1px solid #f8f8f8;
      font-size: 13px;
      align-items: center;
      &:last-child {
        border-bottom: none;
      }
      .order-asset {
        font-weight: bold;
      }
      .order-date, .order-end {
        color: #888;
      }
      .order-profit {
        display: flex;
        flex-direction: column;
        gap: 2px;
      }
    }
  }
}
.time-dropdown-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
  animation: fadeIn 0.2s ease-out;
}

.time-dropdown-grid {
  background: #fff;
  border-radius: 16px;
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.15);
  width: 90%;
  max-width: 320px;
  transform: translateY(0);
  animation: slideUp 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);
}

.time-dropdown-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 8px;
  border-radius: 12px;
  
  &:hover {
    background: rgba(27, 196, 125, 0.08);
  }
  
  &.selected {
    .time-progress circle:nth-child(2) {
      stroke: #1bc47d;
      stroke-width: 3.5px;
    }
    .time-label {
      color: #1bc47d;
      font-weight: 600;
    }
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.time-progress {
  position: relative;
  margin-bottom: 6px;
  width: 48px;
  height: 48px;
  
  circle {
    transition: all 0.3s ease;
  }
  
  circle:nth-child(1) {
    stroke: #f0f0f0;
    stroke-width: 3px;
  }
  
  circle:nth-child(2) {
    stroke: #e0e0e0;
    stroke-width: 3px;
  }
  
  text {
    font-size: 14px;
    font-weight: 600;
    fill: #333;
  }
}

.time-label {
  color: #555;
  font-size: 13px;
  font-weight: 500;
  margin-top: 4px;
  transition: all 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}
.chart-tabs-bar {
  display: flex;
  align-items: center;
  gap: 0;
  padding: 0 0 0 10px;
  margin-bottom: 2px;
  margin-top: 2px;
  background: #fff;
  .chart-tab {
    font-size: 13px;
    color: #888;
    font-weight: 500;
    padding: 0 12px 8px 12px;
    cursor: pointer;
    position: relative;
    transition: color 0.2s;
    &:hover {
      color: #222;
    }
    &.active {
      color: #222;
      font-weight: 700;
      &::after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: 2px;
        transform: translateX(-50%);
        width: 22px;
        height: 2px;
        background: #1bc47d;
        border-radius: 2px;
      }
    }
  }
}
.chart-xaxis-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: absolute;
  left: 0;
  right: 0;
  bottom: -36px;
  height: 32px;
  padding: 0 36px 0 48px;
  z-index: 5;
  background: transparent;
  .chart-xaxis-label {
    font-size: 12px;
    color: #bbb;
    font-weight: 500;
    flex: 1;
    text-align: center;
    &:first-child { text-align: left; }
    &:last-child { text-align: right; }
  }
}
.chart-action-icons-below {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 18px;
  margin-top: 10px;
  margin-bottom: 8px;
  padding-right: 0px;
  padding-left: 26px;
  .chart-btn {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
    cursor: pointer;
    transition: box-shadow 0.2s;
    svg { display: block; }
  }
  .chart-btn-refresh {
    background: #3498db;
    margin-left: 26px;
  }
  .chart-btn-search {
    background: #1bc47d;
  }
  .chart-btn-order {
    background: #f66;
    margin-right: 32px;
  }
}
.pair-dropdown-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.1);
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.pair-dropdown-list {
  position: absolute;
  left: 0;
  top: 100%;
  min-width: 120px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  padding: 8px 0;
  z-index: 20;
  margin-top: 4px;
}
.pair-dropdown-item {
  padding: 8px 14px;
  font-size: 13px;
  color: #222;
  cursor: pointer;
  transition: background 0.2s;
  &:hover {
    background: #f8f9fa;
  }
  &.selected {
    color: #1bc47d;
    font-weight: bold;
    background: #eafaf3;
  }
}
.account-dropdown-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.1);
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}
.account-dropdown-list {
  margin-top: 60px;
  margin-right: 18px;
  min-width: 110px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  padding: 8px 0;
  z-index: 20;
}
.account-dropdown-item {
  padding: 10px 18px;
  font-size: 15px;
  color: #222;
  cursor: pointer;
  transition: background 0.2s;
  text-align: center;
  &:hover {
    background: #f8f9fa;
  }
  &.selected {
    color: #1bc47d;
    font-weight: bold;
    background: #eafaf3;
  }
}
.indicator-dropdown-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.1);
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}
.indicator-dropdown-list {
  margin-top: 38px;
  margin-right: 18px;
  min-width: 90px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  padding: 8px 0;
  z-index: 20;
}
.indicator-dropdown-item {
  padding: 8px 14px;
  font-size: 13px;
  color: #222;
  cursor: pointer;
  transition: background 0.2s;
  &:hover {
    background: #f8f9fa;
  }
  &.selected {
    color: #1bc47d;
    font-weight: bold;
    background: #eafaf3;
  }
}
@media screen and (min-width: 768px) {
  .trade-page, .trade-info-card, .order-list-area {
    max-width: 600px;
    margin: 0 auto;
  }
  
  .user-bar {
    padding: 20px 24px;
    border-radius: 0 0 12px 12px;
    margin: 0 12px;
    
    .avatar {
      width: 48px;
      height: 48px;
    }
    
    .user-info {
      .user-name {
        font-size: 17px;
      }
      .user-type {
        font-size: 14px;
      }
    }
    
    .user-balance {
      .balance {
        font-size: 17px;
      }
      .account-type-row .account-type {
        font-size: 14px;
      }
    }
  }
  
  .pair-bar {
    padding: 18px 20px;
    margin: 0 12px;
    border-radius: 8px;
    margin-top: 8px;
    
    .pair-select {
      font-size: 17px;
    }
    
    .pair-rate {
      font-size: 16px;
    }
    
    .pair-icons svg {
      width: 24px;
      height: 24px;
    }
  }
  
  .chart-area {
    margin: 0 12px;
    border-radius: 8px;
    margin-top: 1px;
    
    .chart-tabs span {
      font-size: 15px;
      padding: 14px 8px;
    }
    
    .chart-mock {
      height: 240px;
      padding: 20px;
    }
  }
  
  .btn-rise, .btn-fall {
    height: 52px;
    font-size: 16px;
  }
  
  .order-tabs span {
    font-size: 15px;
    padding: 10px 0 8px;
  }
  
  .order-header-row {
    font-size: 12px;
  }
  
  .order-item {
    font-size: 14px;
  }
}
</style> 