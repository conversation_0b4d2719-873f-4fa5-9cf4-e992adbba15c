<template>
  <div class="transfer-page">
    <!-- 顶部栏 -->
    <div class="header">
      <button class="icon-btn" @click="$router.back()">
        <svg width="20" height="20" viewBox="0 0 24 24"><path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/></svg>
      </button>
      <span class="title">{{ $t('transfer.title') }}</span>
    </div>

    <!-- 划转方向 -->
    <div class="section direction-section">
      <div class="accounts">
        <div class="from-account">
          <div class="account-label">{{ $t('transfer.from') }}</div>
          <select v-model="fromAccount" class="account-select">
            <option v-for="account in accounts" :key="account.id" :value="account.id">
              {{ account.name }}
            </option>
          </select>
        </div>
        
        <button class="switch-btn" @click="switchAccounts">
          <svg width="24" height="24" viewBox="0 0 24 24">
            <path d="M8 5l4-4 4 4M8 19l4 4 4-4M12 1v22" stroke="#666" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
        
        <div class="to-account">
          <div class="account-label">{{ $t('transfer.to') }}</div>
          <select v-model="toAccount" class="account-select">
            <option v-for="account in accounts" :key="account.id" :value="account.id">
              {{ account.name }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- 币种选择 -->
    <div class="section currency-section">
      <div class="section-title">{{ $t('transfer.selectCurrency') }}</div>
      <div class="currency-list">
        <div
          v-for="currency in currencies"
          :key="currency.id"
          :class="['currency-item', { active: selectedCurrency === currency.id }]"
          @click="selectCurrency(currency)"
        >
          <div class="currency-icon" v-html="currency.icon"></div>
          <div class="currency-info">
            <span class="currency-name">{{ currency.name }}</span>
            <span class="currency-balance">{{ $t('transfer.available') }}{{ getAccountBalance(fromAccount, currency.id) }} {{ currency.symbol }}</span>
          </div>
          <div class="currency-check">
            <svg v-if="selectedCurrency === currency.id" width="24" height="24" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="12" fill="#1bc47d"/>
              <path d="M8 12l3 3 5-5" stroke="#fff" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 划转金额 -->
    <div class="section amount-section">
      <div class="section-title">{{ $t('transfer.transferAmount') }}</div>
      <div class="amount-input-container">
        <input 
          type="text" 
          v-model="amount" 
          class="amount-input" 
          :placeholder="$t('transfer.amountPlaceholder')"
          @input="validateAmount"
        />
        <span class="amount-symbol">{{ selectedCurrencyInfo?.symbol }}</span>
      </div>
      <div class="amount-info">
        <span>{{ $t('transfer.availableBalance') }}{{ getAccountBalance(fromAccount, selectedCurrency) }} {{ selectedCurrencyInfo?.symbol }}</span>
      </div>
      <button class="max-btn" @click="useMaxAmount">{{ $t('transfer.maxTransfer') }}</button>
    </div>

    <!-- 确认按钮 -->
    <button 
      class="confirm-btn" 
      :disabled="isButtonDisabled"
      @click="confirmTransfer"
    >
      {{ isProcessing ? $t('transfer.processing') : $t('transfer.confirmTransfer') }}
    </button>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()
const amount = ref('')
const isProcessing = ref(false)

// 账户列表
const accounts = [
  { id: 'spot', name: t('transfer.accounts.spot') },
  { id: 'futures', name: t('transfer.accounts.futures') },
  { id: 'margin', name: t('transfer.accounts.margin') }
]

// 币种列表
const currencies = [
  { 
    id: 'btc', 
    name: 'Bitcoin', 
    symbol: 'BTC',
    balances: {
      spot: '0.5234',
      futures: '0.1234',
      margin: '0.0234'
    },
    icon: `<svg width="32" height="32" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="#F7931A"/><text x="16" y="22" text-anchor="middle" font-size="16" fill="#fff" font-family="Arial Black, Arial, sans-serif" font-weight="900">₿</text></svg>`
  },
  { 
    id: 'eth', 
    name: 'Ethereum',
    symbol: 'ETH',
    balances: {
      spot: '2.3451',
      futures: '1.2345',
      margin: '0.3456'
    },
    icon: `<svg width="32" height="32" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="#627EEA"/><text x="16" y="22" text-anchor="middle" font-size="16" fill="#fff" font-family="Arial Black, Arial, sans-serif" font-weight="900">Ξ</text></svg>`
  },
  { 
    id: 'usdt', 
    name: 'USDT',
    symbol: 'USDT',
    balances: {
      spot: '1234.56',
      futures: '567.89',
      margin: '890.12'
    },
    icon: `<svg width="32" height="32" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="#26A17B"/><text x="16" y="22" text-anchor="middle" font-size="16" fill="#fff" font-family="Arial Black, Arial, sans-serif" font-weight="900">T</text></svg>`
  }
]

const fromAccount = ref('spot')
const toAccount = ref('futures')
const selectedCurrency = ref('btc')

// 获取选中的币种信息
const selectedCurrencyInfo = computed(() => 
  currencies.find(c => c.id === selectedCurrency.value)
)

// 切换账户方向
const switchAccounts = () => {
  const temp = fromAccount.value
  fromAccount.value = toAccount.value
  toAccount.value = temp
}

// 获取账户余额
const getAccountBalance = (accountId, currencyId) => {
  const currency = currencies.find(c => c.id === currencyId)
  return currency ? currency.balances[accountId] : '0'
}

// 选择币种
const selectCurrency = (currency) => {
  selectedCurrency.value = currency.id
  amount.value = ''
}

// 验证金额输入
const validateAmount = () => {
  // 只允许数字和小数点
  amount.value = amount.value.replace(/[^\d.]/g, '')
  
  // 确保只有一个小数点
  const parts = amount.value.split('.')
  if (parts.length > 2) {
    amount.value = parts[0] + '.' + parts.slice(1).join('')
  }
  
  // 限制小数点后八位
  if (parts.length > 1 && parts[1].length > 8) {
    amount.value = parts[0] + '.' + parts[1].substring(0, 8)
  }
}

// 使用最大可用金额
const useMaxAmount = () => {
  const balance = getAccountBalance(fromAccount.value, selectedCurrency.value)
  amount.value = balance
}

// 计算按钮是否可用
const isButtonDisabled = computed(() => {
  if (!amount.value || isProcessing.value) return true
  if (fromAccount.value === toAccount.value) return true
  
  const value = parseFloat(amount.value)
  const balance = parseFloat(getAccountBalance(fromAccount.value, selectedCurrency.value))
  
  return value <= 0 || value > balance
})

// 确认划转
const confirmTransfer = () => {
  if (isButtonDisabled.value) return
  
  isProcessing.value = true
  
  // 显示确认信息
  console.log(t('common.console.transferConfirm', { 
    amount: amount.value, 
    symbol: selectedCurrencyInfo.value.symbol, 
    from: fromAccount.value, 
    to: toAccount.value 
  }))
  
  // 模拟API调用
  setTimeout(() => {
    alert(t('common.alerts.transferSuccess'))
    isProcessing.value = false
    router.push('/asset')
  }, 1500)
}
</script>

<style scoped lang="scss">
.transfer-page {
  min-height: 100vh;
  background: #f8f8f8;
}

.header {
  display: grid;
  grid-template-columns: 40px 1fr 40px;
  align-items: center;
  padding: calc(env(safe-area-inset-top) + 12px) 0 18px;
  background: #fff;
  border-bottom: 1px solid #eee;
  position: relative;
  .icon-btn {
    grid-column: 1;
    justify-self: start;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: #222;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s;
    &:active {
      background: #f5f5f5;
      transform: scale(0.95);
    }
  }
  .title {
    grid-column: 2;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    pointer-events: none;
  }
}

.section {
  margin-top: 12px;
  background: #fff;
  padding: 16px;
  
  .section-title {
    font-size: 16px;
    color: #666;
    margin-bottom: 16px;
  }
}

.accounts {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .from-account,
  .to-account {
    flex: 1;
    
    .account-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }
    
    .account-select {
      width: 100%;
      padding: 12px;
      border: 1px solid #eee;
      border-radius: 8px;
      background: #fff;
      font-size: 16px;
      color: #333;
      outline: none;
      cursor: pointer;
      
      &:focus {
        border-color: #1bc47d;
      }
    }
  }
  
  .switch-btn {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: none;
    cursor: pointer;
    margin: 0 16px;
    
    &:hover {
      opacity: 0.8;
    }
  }
}

.currency-list {
  .currency-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid #eee;
    border-radius: 12px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.2s;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &.active {
      border-color: #1bc47d;
      background: #f0fff7;
    }
    
    .currency-icon {
      margin-right: 12px;
    }
    
    .currency-info {
      flex: 1;
      
      .currency-name {
        font-size: 16px;
        color: #333;
        margin-bottom: 4px;
        display: block;
      }
      
      .currency-balance {
        font-size: 14px;
        color: #999;
      }
    }
  }
}

.amount-input-container {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 12px;
  
  .amount-input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 18px;
    color: #333;
    outline: none;
    
    &::placeholder {
      color: #999;
    }
  }
  
  .amount-symbol {
    font-size: 16px;
    color: #666;
    margin-left: 8px;
  }
}

.amount-info {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.max-btn {
  width: 100%;
  padding: 12px;
  border: 1px solid #1bc47d;
  border-radius: 8px;
  background: #fff;
  color: #1bc47d;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background: #f0fff7;
  }
}

.confirm-btn {
  width: 90%;
  margin: 32px auto;
  display: block;
  background: #1bc47d;
  color: #fff;
  border: none;
  border-radius: 12px;
  font-size: 20px;
  font-weight: bold;
  padding: 14px 0;
  letter-spacing: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
  
  &:hover:not(:disabled) {
    background: #18b371;
  }
}
</style>