<template>
  <div class="verify-page">
    <!-- 装饰背景元素 -->
    <div class="decoration-circle circle-1"></div>
    <div class="decoration-circle circle-2"></div>
    <div class="decoration-circle circle-3"></div>

    <div class="verify-container">
      <!-- 返回按钮 -->
      <router-link to="/register" class="back-btn">
        <span class="back-arrow">←</span>
        {{ $t('common.back') }}
      </router-link>

      <div class="form-container">
        <h1 class="title">{{ $t('verifyCode.title') }}</h1>
        <p class="subtitle">
          {{ $t('verifyCode.subtitle', { email: maskedEmail }) }}
        </p>

        <!-- 验证码输入框 -->
        <div class="code-input-container">
          <div
            v-for="(digit, index) in codeLength"
            :key="index"
            class="code-input-wrapper"
            :class="{ 'has-error': hasError }"
          >
            <input
              type="text"
              maxlength="1"
              v-model="codeDigits[index]"
              @input="handleInput($event, index)"
              @keydown="handleKeydown($event, index)"
              @paste="handlePaste"
              ref="inputs"
            />
          </div>
        </div>

        <!-- 错误提示 - 隐式提醒 -->
        <transition name="fade">
          <div v-if="hasError" class="error-notification">
            <div class="error-icon">!</div>
            <p class="error-message">{{ errorMessage }}</p>
          </div>
        </transition>

        <!-- 重发验证码 -->
        <div class="resend-container">
          <template v-if="countdown > 0">
            <p class="countdown">
              {{ $t('verifyCode.countdown', { seconds: countdown }) }}
            </p>
          </template>
          <button
            v-else
            @click="resendCode"
            class="resend-btn"
            :disabled="isResending"
          >
            {{ $t('verifyCode.resend') }}
          </button>
        </div>

        <!-- 验证按钮 -->
        <button
          @click="verifyCode"
          class="verify-btn"
          :disabled="!isComplete || isVerifying"
        >
          <span v-if="!isVerifying">{{ $t('verifyCode.verify') }}</span>
          <span v-else class="loading-spinner"></span>
        </button>

        <!-- 帮助提示 -->
        <div class="help-section">
          <p>{{ $t('verifyCode.help.title') }}</p>
          <ul>
            <li>{{ $t('verifyCode.help.checkSpam') }}</li>
            <li>{{ $t('verifyCode.help.checkEmail') }}</li>
            <li>
              <button class="help-btn" @click="showHelp">
                {{ $t('verifyCode.help.needHelp') }}
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import userApi from '@/api/user'

const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const codeLength = 6
const codeDigits = ref(Array(codeLength).fill(''))
const inputs = ref([])
const hasError = ref(false)
const errorMessage = ref('')
const isVerifying = ref(false)
const isResending = ref(false)
const countdown = ref(0)
let countdownTimer = null

// 从 sessionStorage 获取注册信息
const password = sessionStorage.getItem('register_password')
const inviteCode = sessionStorage.getItem('register_invite_code')
const email = ref(sessionStorage.getItem('register_email') || route.query.email || '<EMAIL>')
const maskedEmail = computed(() => {
  const [username, domain] = email.value.split('@')
  return `${username.slice(0, 3)}***@${domain}`
})

// 验证码是否完整
const isComplete = computed(() => {
  return codeDigits.value.every(digit => digit !== '')
})

// 处理输入
const handleInput = (event, index) => {
  const value = event.target.value
  if (value.length > 0) {
    codeDigits.value[index] = value
    // 自动跳转到下一个输入框
    if (index < codeLength - 1) {
      inputs.value[index + 1].focus()
    }
  }
  hasError.value = false
  errorMessage.value = ''
}

// 处理键盘事件
const handleKeydown = (event, index) => {
  if (event.key === 'Backspace' && !codeDigits.value[index]) {
    // 当前输入框为空时，删除前一个输入框的内容
    if (index > 0) {
      codeDigits.value[index - 1] = ''
      inputs.value[index - 1].focus()
    }
  }
}

// 处理粘贴事件
const handlePaste = (event) => {
  event.preventDefault()
  const pastedText = event.clipboardData.getData('text')
  const digits = pastedText.replace(/\D/g, '').slice(0, codeLength)
  
  digits.split('').forEach((digit, index) => {
    if (index < codeLength) {
      codeDigits.value[index] = digit
    }
  })
}

// 验证码验证
const verifyCode = async () => {
  if (!isComplete.value) return
  
  isVerifying.value = true
  const captcha = codeDigits.value.join('')
  try {
    // 调用API验证验证码
    const response = await userApi.verifyEmailCode({
      email: email.value,
      captcha: captcha,
      inviteCode: inviteCode,
      password: password
    })
    // 处理 code=0 且 success=true 的情况，自动注册
    if (response.code === 0 && response.success === true) {
      // 验证成功后自动注册
      if (!password) {
        alert(t('register.failed') || '注册信息缺失，请返回重新注册')
        return
      }
      // 用完后清除 sessionStorage
      sessionStorage.removeItem('register_email')
      sessionStorage.removeItem('register_password')
      sessionStorage.removeItem('register_invite_code')
      router.push('/register-success')
    } else {
      // 验证失败
      throw { response: { data: { message: response.msg || t('verifyCode.errorMessage') } } }
    }
  } catch (error) {
    console.error('验证失败:', error)
    hasError.value = true
    errorMessage.value = error.response?.data?.message || t('verifyCode.errorMessage')
    codeDigits.value = Array(codeLength).fill('')
    inputs.value[0].focus()
  } finally {
    isVerifying.value = false
  }
}

// 重发验证码
const resendCode = async () => {
  if (isResending.value || countdown.value > 0) return
  
  isResending.value = true
  try {
    // 调用API重新发送验证码
    const response = await userApi.sendEmailVerificationCode({
      email: email.value
    })
    
    // 处理 code=0 且 success=true 的情况
    if ((response.code === 0 && response.success === true) || response.success) {
      // 重置倒计时
      countdown.value = 60
      startCountdown()
      
      // 显示成功消息
      hasError.value = false
      errorMessage.value = t('verifyCode.resendSuccess')
    } else {
      throw { response: { data: { message: response.msg || t('verifyCode.resendError') } } }
    }
  } catch (error) {
    console.error('重发验证码失败:', error)
    hasError.value = true
    errorMessage.value = error.response?.data?.message || t('verifyCode.resendError')
  } finally {
    isResending.value = false
  }
}

// 倒计时
const startCountdown = () => {
  countdownTimer = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else {
      clearInterval(countdownTimer)
    }
  }, 1000)
}

// 显示帮助
const showHelp = () => {
  // 这里添加显示帮助的逻辑
  console.log('Show help modal')
}

// 组件挂载时启动倒计时
onMounted(() => {
  countdown.value = 60
  startCountdown()
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped lang="scss">
.verify-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 20px;
}

// 装饰圆圈样式（与其他页面保持一致）
.decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  
  &.circle-1 {
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, #007bff, #00ff88);
    top: -100px;
    right: -100px;
    animation: float 8s ease-in-out infinite;
  }
  
  &.circle-2 {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, #ff3366, #ff9933);
    bottom: -50px;
    left: -50px;
    animation: float 6s ease-in-out infinite reverse;
  }
  
  &.circle-3 {
    width: 150px;
    height: 150px;
    background: linear-gradient(45deg, #6610f2, #6f42c1);
    top: 50%;
    right: -75px;
    animation: float 7s ease-in-out infinite;
  }
}

.verify-container {
  width: 100%;
  max-width: 480px;
  position: relative;
  z-index: 1;
}

.back-btn {
  display: inline-flex;
  align-items: center;
  color: #666;
  text-decoration: none;
  font-size: 16px;
  margin-bottom: 24px;
  transition: color 0.3s ease;
  
  .back-arrow {
    margin-right: 8px;
    transition: transform 0.3s ease;
  }
  
  &:hover {
    color: #111;
    
    .back-arrow {
      transform: translateX(-4px);
    }
  }
}

.form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.6s ease-out forwards;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #111;
  margin-bottom: 8px;
}

.subtitle {
  color: #666;
  font-size: 16px;
  margin-bottom: 32px;
}

.code-input-container {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 24px;
}

.code-input-wrapper {
  input {
    width: 55px;
    height: 65px;
    border: 2px solid #e0e0e0;
    border-radius: 16px;
    font-size: 26px;
    font-weight: 700;
    text-align: center;
    transition: all 0.3s ease;
    background: #fafafa;
    
    &:focus {
      border-color: #111;
      outline: none;
      box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1);
      background: #fff;
      transform: translateY(-2px);
    }
  }
  
  &.has-error input {
    border-color: #dc3545;
    animation: shake 0.5s ease-in-out;
    background: #fff5f5;
  }
}

// 错误提示的隐式样式
.error-notification {
  display: flex;
  align-items: center;
  background-color: rgba(255, 235, 235, 0.9);
  border-left: 3px solid #ff4d4f;
  border-radius: 4px;
  padding: 10px 15px;
  margin: 0 0 24px;
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.15);
  animation: slideIn 0.3s ease-out;
  text-align: left;
}

.error-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 50%;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.error-message {
  color: #cf1322;
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

.resend-container {
  margin-bottom: 24px;
  
  .countdown {
    color: #666;
    font-size: 14px;
    margin-bottom: 24px;
  }
  
  .code-input-container {
    gap: 8px;
  }
  
  .code-input-wrapper {
    input {
      width: 40px;
      height: 50px;
      font-size: 20px;
    }
  }
  
  .verify-btn {
    padding: 14px;
  }
  
  .help-section {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .code-input-wrapper {
    input {
      width: 35px;
      height: 45px;
      font-size: 18px;
    }
  }
}

.resend-btn {
    background: none;
    border: none;
    color: #111;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 8px;
    
    &:hover:not(:disabled) {
      background: rgba(0, 0, 0, 0.05);
      text-decoration: none;
    }
    
    &:disabled {
      color: #666;
      cursor: not-allowed;
    }
  }

.verify-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(45deg, #111 0%, #333 100%);
  color: #fff;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 32px;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.help-section {
  color: #666;
  font-size: 14px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  padding: 20px;
  margin-top: 24px;
  
  p {
    margin-bottom: 12px;
    font-weight: 500;
    color: #333;
  }
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      margin-bottom: 8px;
      padding-left: 16px;
      position: relative;
      
      &:before {
        content: '•';
        position: absolute;
        left: 0;
        color: #999;
      }
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .help-btn {
    background: none;
    border: none;
    color: #111;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 4px 8px;
    border-radius: 6px;
    
    &:hover {
      background: rgba(0, 0, 0, 0.05);
      text-decoration: none;
    }
  }
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

// 动画关键帧
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  20%, 60% {
    transform: translateX(-5px);
  }
  40%, 80% {
    transform: translateX(5px);
  }
}

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-container {
    padding: 30px 20px;
  }
  
  .title {
    font-size: 28px;
  }
  
  .subtitle {
    font-size: 14px
  }
  
  .register-btn {
    padding: 14px;
  }
  
  .help-section {
    font-size: 14px;
    
    p {
      margin-bottom: 12px;
    }
    
    ul {
      li {
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>