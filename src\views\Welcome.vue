<template>
  <div class="welcome">
    <div class="header">
      <h1 v-html="$t('welcome.title')"></h1>
      <p>{{ $t('welcome.subtitle') }}</p>
    </div>
    <div class="chart-container">
      <div class="chart-card">
        <div class="price">114.83</div>
        <div class="chart">
          <!-- 使用静态图表图片 -->
          <svg viewBox="0 0 200 100" class="trend-line">
            <path d="M0,50 C50,30 100,60 200,20" stroke="#00C853" fill="none" stroke-width="2"/>
          </svg>
        </div>
        <div class="chart-footer">
          <span>{{ $t('welcome.time24h') }}</span>
          <span class="up">+3.21%</span>
        </div>
      </div>
    </div>
    <TabBar />
  </div>
</template>

<script setup>
import TabBar from '../components/TabBar.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
</script>

<style scoped lang="scss">
@use '../styles/variables.scss' as *;

.welcome {
  min-height: 100vh;
  background: $secondary;
  padding: 48px 24px;

  .header {
    text-align: center;
    margin-bottom: 48px;

    h1 {
      font-size: 32px;
      font-weight: bold;
      margin-bottom: 16px;
      line-height: 1.4;
    }

    p {
      color: #666;
      font-size: 16px;
    }
  }

  .chart-container {
    padding: 0 16px;
    
    .chart-card {
      background: $primary;
      border-radius: $radius;
      padding: 24px;
      color: $secondary;

      .price {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 16px;
      }

      .chart {
        height: 120px;
        margin: 24px 0;

        .trend-line {
          width: 100%;
          height: 100%;
        }
      }

      .chart-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .up {
          color: $success;
        }
      }
    }
  }
}
</style> 