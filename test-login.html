<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>登录功能测试</h1>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="email">邮箱/用户名:</label>
            <input type="text" id="email" value="admin" required>
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="123456" required>
        </div>
        
        <div class="form-group">
            <label for="googleCode">Google验证码 (可选):</label>
            <input type="text" id="googleCode" placeholder="6位数字" maxlength="6">
        </div>
        
        <button type="submit" id="loginBtn">登录</button>
    </form>
    
    <div id="result"></div>

    <script>
        const form = document.getElementById('loginForm');
        const resultDiv = document.getElementById('result');
        const loginBtn = document.getElementById('loginBtn');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const googleCode = document.getElementById('googleCode').value;
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            resultDiv.innerHTML = '';
            
            try {
                // 构建表单数据
                const formData = new URLSearchParams();
                formData.append('username', email);
                formData.append('password', password);
                formData.append('grant_type', 'password');
                
                if (googleCode.trim()) {
                    formData.append('googleCode', googleCode.trim());
                }
                
                const response = await fetch('http://localhost:29998/auth/oauth/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Authorization': 'Basic ' + btoa('customer:customer')
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                resultDiv.className = 'result';
                
                if (data.access_token) {
                    resultDiv.className += ' success';
                    resultDiv.textContent = '登录成功！\n' + JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className += ' error';
                    resultDiv.textContent = '登录失败：\n' + JSON.stringify(data, null, 2);
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '请求失败：' + error.message;
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });
    </script>
</body>
</html>
